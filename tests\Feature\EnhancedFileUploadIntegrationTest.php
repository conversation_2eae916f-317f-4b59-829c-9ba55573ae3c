<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EnhancedFileUploadIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();

        // Create instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);

        // Create chapter
        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
        ]);

        // Create resource lecture
        $this->lecture = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'resource',
            'resources' => [],
        ]);

        // Set up fake storage
        Storage::fake('local');
    }

    /** @test */
    public function course_builder_page_loads_with_enhanced_file_upload_interface()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('course-builder.show', $this->course->slug));

        $response->assertStatus(200)
                ->assertSee('course-builder-file-upload.js')
                ->assertSee('file-upload-zone')
                ->assertSee('Drop files here or click to browse');
    }

    /** @test */
    public function multiple_files_can_be_uploaded_simultaneously()
    {
        $this->actingAs($this->instructor);

        // Create multiple test files
        $files = [
            UploadedFile::fake()->create('document1.pdf', 1024, 'application/pdf'),
            UploadedFile::fake()->create('presentation.pptx', 2048, 'application/vnd.openxmlformats-officedocument.presentationml.presentation'),
            UploadedFile::fake()->image('screenshot.jpg', 800, 600)->size(500),
            UploadedFile::fake()->create('data.csv', 256, 'text/csv'),
        ];

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => $files,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Files uploaded successfully',
                ])
                ->assertJsonStructure([
                    'data' => [
                        'files' => [
                            '*' => [
                                'id',
                                'name',
                                'original_name',
                                'file_path',
                                'file_size',
                                'file_type',
                                'uploaded_at',
                                'download_url',
                            ]
                        ],
                        'total_files'
                    ]
                ]);

        // Verify all files were uploaded
        $this->lecture->refresh();
        $this->assertCount(4, $this->lecture->resources);

        // Verify each file type was handled correctly
        $uploadedFiles = collect($this->lecture->resources);
        $this->assertTrue($uploadedFiles->contains('name', 'document1.pdf'));
        $this->assertTrue($uploadedFiles->contains('name', 'presentation.pptx'));
        $this->assertTrue($uploadedFiles->contains('name', 'screenshot.jpg'));
        $this->assertTrue($uploadedFiles->contains('name', 'data.csv'));

        // Verify files exist in storage
        foreach ($this->lecture->resources as $resource) {
            Storage::disk('local')->assertExists($resource['file_path']);
        }
    }

    /** @test */
    public function file_upload_handles_various_file_types_correctly()
    {
        $this->actingAs($this->instructor);

        $testFiles = [
            ['name' => 'document.pdf', 'mime' => 'application/pdf'],
            ['name' => 'spreadsheet.xlsx', 'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            ['name' => 'archive.zip', 'mime' => 'application/zip'],
            ['name' => 'image.png', 'mime' => 'image/png'],
            ['name' => 'text.txt', 'mime' => 'text/plain'],
        ];

        foreach ($testFiles as $fileData) {
            $file = UploadedFile::fake()->create($fileData['name'], 1024, $fileData['mime']);

            $response = $this->postJson(route('course-builder.lectures.upload-resources', [
                'course' => $this->course->slug,
                'chapter' => $this->chapter->id,
                'lecture' => $this->lecture->id,
            ]), [
                'resource_files' => [$file],
            ]);

            $response->assertStatus(200);
        }

        // Verify all file types were accepted
        $this->lecture->refresh();
        $this->assertCount(5, $this->lecture->resources);
    }

    /** @test */
    public function file_upload_validates_file_size_limits()
    {
        $this->actingAs($this->instructor);

        // Create file larger than 50MB limit
        $oversizedFile = UploadedFile::fake()->create('large.pdf', 52 * 1024, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$oversizedFile],
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resource_files.0']);
    }

    /** @test */
    public function file_upload_validates_maximum_file_count()
    {
        $this->actingAs($this->instructor);

        // Create 11 files (exceeds limit of 10)
        $files = [];
        for ($i = 0; $i < 11; $i++) {
            $files[] = UploadedFile::fake()->create("file{$i}.pdf", 100, 'application/pdf');
        }

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => $files,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resource_files']);
    }

    /** @test */
    public function file_upload_rejects_dangerous_file_types()
    {
        $this->actingAs($this->instructor);

        $dangerousFiles = [
            UploadedFile::fake()->create('malicious.exe', 1024, 'application/x-executable'),
            UploadedFile::fake()->create('script.js', 1024, 'application/javascript'),
            UploadedFile::fake()->create('batch.bat', 1024, 'application/x-bat'),
        ];

        foreach ($dangerousFiles as $file) {
            $response = $this->postJson(route('course-builder.lectures.upload-resources', [
                'course' => $this->course->slug,
                'chapter' => $this->chapter->id,
                'lecture' => $this->lecture->id,
            ]), [
                'resource_files' => [$file],
            ]);

            $response->assertStatus(422);
        }
    }

    /** @test */
    public function uploaded_files_are_stored_with_correct_metadata()
    {
        $this->actingAs($this->instructor);

        $file = UploadedFile::fake()->create('test-document.pdf', 2048, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$file],
        ]);

        $response->assertStatus(200);

        $this->lecture->refresh();
        $uploadedFile = $this->lecture->resources[0];

        // Verify metadata
        $this->assertEquals('test-document.pdf', $uploadedFile['name']);
        $this->assertEquals('test-document.pdf', $uploadedFile['original_name']);
        $this->assertEquals(2048 * 1024, $uploadedFile['file_size']); // 2048 KB in bytes
        $this->assertEquals('application/pdf', $uploadedFile['file_type']);
        $this->assertArrayHasKey('id', $uploadedFile);
        $this->assertArrayHasKey('uploaded_at', $uploadedFile);
        $this->assertArrayHasKey('download_url', $uploadedFile);

        // Verify file path structure
        $expectedPathPrefix = "private/courses/{$this->instructor->id}/{$this->course->id}/materials/resources";
        $this->assertStringStartsWith($expectedPathPrefix, $uploadedFile['file_path']);
    }

    /** @test */
    public function new_files_merge_with_existing_resources()
    {
        $this->actingAs($this->instructor);

        // Add existing resources
        $existingResources = [
            [
                'id' => 'existing-1',
                'name' => 'existing-file.pdf',
                'original_name' => 'existing-file.pdf',
                'file_path' => 'existing/path/file.pdf',
                'file_size' => 1024,
                'file_type' => 'application/pdf',
                'uploaded_at' => now()->toISOString(),
            ]
        ];

        $this->lecture->update(['resources' => $existingResources]);

        // Upload new files
        $newFiles = [
            UploadedFile::fake()->create('new-document.pdf', 2048, 'application/pdf'),
            UploadedFile::fake()->create('new-image.jpg', 1024, 'image/jpeg'),
        ];

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => $newFiles,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'data' => [
                        'total_files' => 3
                    ]
                ]);

        // Verify all files exist
        $this->lecture->refresh();
        $this->assertCount(3, $this->lecture->resources);

        // Verify existing file is preserved
        $this->assertEquals('existing-file.pdf', $this->lecture->resources[0]['name']);

        // Verify new files are added
        $fileNames = collect($this->lecture->resources)->pluck('name')->toArray();
        $this->assertContains('new-document.pdf', $fileNames);
        $this->assertContains('new-image.jpg', $fileNames);
    }

    /** @test */
    public function unauthorized_users_cannot_upload_files()
    {
        // Create different instructor
        $otherInstructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $this->actingAs($otherInstructor);

        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$file],
        ]);

        $response->assertStatus(403);
    }
}
