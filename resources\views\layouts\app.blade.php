<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Escape Matrix Academy - Transform Your Life')</title>
    <meta name="description" content="@yield('description', 'Break free from limitations with courses in AI, coding, business, fitness, and personal development.')">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700,800,900" rel="stylesheet" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <link rel="stylesheet" href="{{ asset('css/navigation.css') }}">
    
    <!-- Additional Styles -->
    @stack('styles')
</head>
<body class="bg-black text-white font-sans antialiased">
    <!-- Floating Navigation -->
    <nav class="floating-nav" id="floatingNav">
        <div class="nav-container">
            <a href="{{ route('home') }}" class="nav-brand">
                Escape Matrix Academy
            </a>

            <!-- Desktop Navigation -->
            <div class="nav-links">
                <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                    Home
                </a>
                <a href="{{ route('courses.index') }}" class="nav-link {{ request()->routeIs('courses.*') ? 'active' : '' }}">
                    Courses
                </a>
                <a href="{{ route('about') }}" class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}">
                    About
                </a>
                <a href="{{ route('contact') }}" class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}">
                    Contact
                </a>

                @auth
                    <!-- User Menu -->
                    <div class="user-menu-container">
                        <button class="user-menu-button" 
                                onclick="toggleUserMenu()" 
                                aria-expanded="false" 
                                aria-haspopup="true">
                            <div class="user-avatar">
                                {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                            </div>
                            <span class="user-name">{{ auth()->user()->name }}</span>
                            <svg class="dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Floating Dropdown Menu -->
                        <div id="userMenu" class="floating-dropdown">
                            <!-- User Info Header -->
                            <div class="dropdown-user-info">
                                <div class="dropdown-user-avatar">
                                    {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                                </div>
                                <div class="dropdown-user-details">
                                    <p class="dropdown-user-name">{{ auth()->user()->name }}</p>
                                    <p class="dropdown-user-email">{{ auth()->user()->email }}</p>
                                </div>
                            </div>

                            <!-- Navigation Items -->
                            <div class="dropdown-nav">
                                <a href="{{ route('dashboard') }}" class="dropdown-item">
                                    <div class="dropdown-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    Dashboard
                                </a>
                                @if(auth()->user()->isInstructor())
                                    <a href="{{ route('instructor.dashboard') }}" class="dropdown-item">
                                        <div class="dropdown-icon">
                                            <i class="fas fa-chalkboard-teacher"></i>
                                        </div>
                                        Instructor Dashboard
                                    </a>
                                @endif
                                @if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                                    <a href="{{ route('admin.dashboard') }}" class="dropdown-item">
                                        <div class="dropdown-icon">
                                            <i class="fas fa-cogs"></i>
                                        </div>
                                        Admin Dashboard
                                    </a>
                                @endif
                                <a href="{{ route('my-courses') }}" class="dropdown-item">
                                    <div class="dropdown-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    My Courses
                                </a>
                                <a href="{{ route('payments.history') }}" class="dropdown-item">
                                    <div class="dropdown-icon">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    Payment History
                                </a>
                                <a href="{{ route('profile.show') }}" class="dropdown-item">
                                    <div class="dropdown-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    Profile Settings
                                </a>
                            </div>

                            <!-- Logout Section -->
                            <div class="dropdown-logout">
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="logout-button">
                                        <div class="dropdown-icon">
                                            <i class="fas fa-sign-out-alt"></i>
                                        </div>
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <a href="{{ route('login') }}" class="nav-link">
                        Login
                    </a>
                    <a href="{{ route('register') }}" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        Get Started
                    </a>
                @endauth
            </div>

            <!-- Mobile menu button -->
            <button class="mobile-menu-button" onclick="toggleMobileMenu()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobileMenu" class="mobile-nav">
            <div class="mobile-nav-content">
                <a href="{{ route('home') }}" class="mobile-nav-item {{ request()->routeIs('home') ? 'active' : '' }}">
                    Home
                </a>
                <a href="{{ route('courses.index') }}" class="mobile-nav-item {{ request()->routeIs('courses.*') ? 'active' : '' }}">
                    Courses
                </a>
                <a href="{{ route('about') }}" class="mobile-nav-item {{ request()->routeIs('about') ? 'active' : '' }}">
                    About
                </a>
                <a href="{{ route('contact') }}" class="mobile-nav-item {{ request()->routeIs('contact') ? 'active' : '' }}">
                    Contact
                </a>
                @auth
                    <a href="{{ route('dashboard') }}" class="mobile-nav-item">
                        Dashboard
                    </a>
                    @if(auth()->user()->isInstructor())
                        <a href="{{ route('instructor.dashboard') }}" class="mobile-nav-item">
                            Instructor Dashboard
                        </a>
                    @endif
                    @if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                        <a href="{{ route('admin.dashboard') }}" class="mobile-nav-item">
                            Admin Dashboard
                        </a>
                    @endif
                    <a href="{{ route('my-courses') }}" class="mobile-nav-item">
                        My Courses
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="mobile-nav-item w-full text-left">
                            Logout
                        </button>
                    </form>
                @else
                    <a href="{{ route('login') }}" class="mobile-nav-item">
                        Login
                    </a>
                    <a href="{{ route('register') }}" class="mobile-nav-item bg-red-600 rounded-lg hover:bg-red-700">
                        Get Started
                    </a>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
        @if(session('success'))
            <div class="toast-notification bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg border-l-4 border-green-400 flex items-center space-x-3 max-w-md">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-200"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">Success!</p>
                    <p class="text-sm text-green-100">{{ session('success') }}</p>
                </div>
                <button type="button" class="flex-shrink-0 text-green-200 hover:text-white" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        @endif

        @if(session('error'))
            <div class="toast-notification bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg border-l-4 border-red-400 flex items-center space-x-3 max-w-md">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-200"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">Error!</p>
                    <p class="text-sm text-red-100">{{ session('error') }}</p>
                </div>
                <button type="button" class="flex-shrink-0 text-red-200 hover:text-white" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        @endif

        @if(session('warning'))
            <div class="toast-notification bg-yellow-600 text-white px-6 py-4 rounded-lg shadow-lg border-l-4 border-yellow-400 flex items-center space-x-3 max-w-md">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-200"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">Warning!</p>
                    <p class="text-sm text-yellow-100">{{ session('warning') }}</p>
                </div>
                <button type="button" class="flex-shrink-0 text-yellow-200 hover:text-white" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        @endif

        @if(session('info'))
            <div class="toast-notification bg-blue-600 text-white px-6 py-4 rounded-lg shadow-lg border-l-4 border-blue-400 flex items-center space-x-3 max-w-md">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-200"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">Info</p>
                    <p class="text-sm text-blue-100">{{ session('info') }}</p>
                </div>
                <button type="button" class="flex-shrink-0 text-blue-200 hover:text-white" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        @endif
    </div>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4 text-red-500">Escape Matrix Academy</h3>
                    <p class="text-gray-400">
                        Empowering individuals to break free from traditional limitations through transformative education.
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="{{ route('courses.index') }}" class="hover:text-white">Courses</a></li>
                        <li><a href="{{ route('about') }}" class="hover:text-white">About</a></li>
                        <li><a href="{{ route('contact') }}" class="hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Categories</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="{{ route('courses.category', 'ai') }}" class="hover:text-white">AI & Technology</a></li>
                        <li><a href="{{ route('courses.category', 'business') }}" class="hover:text-white">Business</a></li>
                        <li><a href="{{ route('courses.category', 'coding') }}" class="hover:text-white">Programming</a></li>
                        <li><a href="{{ route('courses.category', 'fitness') }}" class="hover:text-white">Fitness</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Contact</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>
                            <a href="mailto:<EMAIL>"
                               class="text-red-400 hover:text-red-300 transition-colors text-sm break-words">
                                support@<br class="sm:hidden">escapematrix.academy
                            </a>
                        </li>
                        <li class="text-sm">24/7 Support Available</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} Escape Matrix Academy. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    @stack('scripts')
    
    <script>
        // Floating Navigation System
        document.addEventListener('DOMContentLoaded', function() {
            const floatingNav = document.getElementById('floatingNav');
            const userMenu = document.getElementById('userMenu');
            const mobileMenu = document.getElementById('mobileMenu');
            
            // Scroll effect for navigation
            let lastScrollY = window.scrollY;
            
            function updateNavOnScroll() {
                const currentScrollY = window.scrollY;
                
                if (currentScrollY > 50) {
                    floatingNav.classList.add('scrolled');
                } else {
                    floatingNav.classList.remove('scrolled');
                }
                
                lastScrollY = currentScrollY;
            }
            
            window.addEventListener('scroll', updateNavOnScroll, { passive: true });
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                // Close user menu if clicking outside
                if (userMenu && !userMenu.contains(e.target) && !e.target.closest('.user-menu-button')) {
                    userMenu.classList.remove('show');
                    const button = document.querySelector('.user-menu-button');
                    if (button) button.setAttribute('aria-expanded', 'false');
                }
                
                // Close mobile menu if clicking outside
                if (mobileMenu && !mobileMenu.contains(e.target) && !e.target.closest('.mobile-menu-button')) {
                    mobileMenu.classList.remove('show');
                }
            });
            
            // Escape key to close dropdowns
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (userMenu) {
                        userMenu.classList.remove('show');
                        const button = document.querySelector('.user-menu-button');
                        if (button) button.setAttribute('aria-expanded', 'false');
                    }
                    if (mobileMenu) {
                        mobileMenu.classList.remove('show');
                    }
                }
            });
        });
        
        // Toggle User Menu
        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            const button = document.querySelector('.user-menu-button');
            
            if (userMenu && button) {
                const isOpen = userMenu.classList.contains('show');
                
                if (isOpen) {
                    userMenu.classList.remove('show');
                    button.setAttribute('aria-expanded', 'false');
                } else {
                    userMenu.classList.add('show');
                    button.setAttribute('aria-expanded', 'true');
                    
                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobileMenu');
                    if (mobileMenu) {
                        mobileMenu.classList.remove('show');
                    }
                }
            }
        }
        
        // Toggle Mobile Menu
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            
            if (mobileMenu) {
                const isOpen = mobileMenu.classList.contains('show');
                
                if (isOpen) {
                    mobileMenu.classList.remove('show');
                } else {
                    mobileMenu.classList.add('show');
                    
                    // Close user menu if open
                    const userMenu = document.getElementById('userMenu');
                    if (userMenu) {
                        userMenu.classList.remove('show');
                        const button = document.querySelector('.user-menu-button');
                        if (button) button.setAttribute('aria-expanded', 'false');
                    }
                }
            }
        }
    </script>

    <!-- Toast Notification Scripts -->
    <script>
    // Auto-hide toast notifications after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const toasts = document.querySelectorAll('.toast-notification');
        toasts.forEach(toast => {
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }, 5000);
        });
    });

    // Function to show dynamic toast notifications
    function showToast(type, title, message) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const iconMap = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };

        const colorMap = {
            'success': 'bg-green-600 border-green-400 text-green-200',
            'error': 'bg-red-600 border-red-400 text-red-200',
            'warning': 'bg-yellow-600 border-yellow-400 text-yellow-200',
            'info': 'bg-blue-600 border-blue-400 text-blue-200'
        };

        const toast = document.createElement('div');
        toast.className = `toast-notification ${colorMap[type]} text-white px-6 py-4 rounded-lg shadow-lg border-l-4 flex items-center space-x-3 max-w-md transform translate-x-full opacity-0 transition-all duration-300`;

        toast.innerHTML = `
            <div class="flex-shrink-0">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="flex-1">
                <p class="font-medium">${title}</p>
                <p class="text-sm opacity-90">${message}</p>
            </div>
            <button type="button" class="flex-shrink-0 opacity-70 hover:opacity-100" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(toast);

        // Trigger animation
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        }, 100);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }, 5000);
    }

    // Global function to show success toast
    window.showSuccess = function(message) {
        showToast('success', 'Success!', message);
    };

    // Global function to show error toast
    window.showError = function(message) {
        showToast('error', 'Error!', message);
    };

    // Global function to show warning toast
    window.showWarning = function(message) {
        showToast('warning', 'Warning!', message);
    };

    // Global function to show info toast
    window.showInfo = function(message) {
        showToast('info', 'Info', message);
    };
    </script>
</body>
</html>
