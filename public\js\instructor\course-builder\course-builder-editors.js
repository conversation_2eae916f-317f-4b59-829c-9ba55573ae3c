/**
 * Course Builder Editors Module
 * Handles loading and rendering of different editors (course, chapter, lecture)
 */

function loadEditor(type, id) {
    // Hide all editors
    document.getElementById('welcome-state')?.classList.add('hidden');
    document.getElementById('course-editor')?.classList.add('hidden');
    document.getElementById('chapter-editor')?.classList.add('hidden');
    document.getElementById('lecture-editor')?.classList.add('hidden');

    switch (type) {
        case 'course':
            document.getElementById('course-editor')?.classList.remove('hidden');
            // Auto-save disabled - using manual save instead
            // initializeCourseAutoSave();
            break;
        case 'chapter':
            loadChapterEditor(id);
            break;
        case 'lecture':
            loadLectureEditor(id);
            break;
    }
}

function loadChapterEditor(chapterId) {
    const chapterEditor = document.getElementById('chapter-editor');
    if (!chapterEditor) return;

    // Show loading state
    chapterEditor.innerHTML = `
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                <p class="text-gray-400">Loading chapter...</p>
            </div>
        </div>
    `;
    chapterEditor.classList.remove('hidden');

    // Fetch chapter data from backend
    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderChapterEditor(chapterId, data.data);
        } else {
            chapterEditor.innerHTML = `
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                        <p class="text-red-400">Failed to load chapter data</p>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading chapter:', error);
        chapterEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                    <p class="text-red-400">Network error loading chapter</p>
                </div>
            </div>
        `;
    });
}

function renderChapterEditor(chapterId, chapterData) {
    const chapterEditor = document.getElementById('chapter-editor');
    if (!chapterEditor) return;

    const learningObjectives = Array.isArray(chapterData.learning_objectives)
        ? chapterData.learning_objectives.join('\n')
        : '';

    chapterEditor.innerHTML = `
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white">Edit Chapter</h3>
                <button onclick="deleteChapter('${chapterId}')"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete Chapter
                </button>
            </div>

            <form id="chapter-form-${chapterId}" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Chapter Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="title" value="${chapterData.title || ''}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                           placeholder="Enter chapter title">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Chapter Description
                    </label>
                    <textarea name="description" rows="4"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Describe what this chapter covers">${chapterData.description || ''}</textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Learning Objectives
                    </label>
                    <textarea name="learning_objectives" rows="4"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Enter learning objectives (one per line)">${learningObjectives}</textarea>
                    <p class="text-xs text-gray-400 mt-1">Enter each learning objective on a new line</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" name="is_published" ${chapterData.is_published ? 'checked' : ''}
                                   class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                            <span class="text-gray-300">Published</span>
                        </label>
                    </div>

                    <div>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" name="is_free_preview" ${chapterData.is_free_preview ? 'checked' : ''}
                                   class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                            <span class="text-gray-300">Free Preview</span>
                        </label>
                    </div>
                </div>
                <!-- Save Button -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                    <button type="button" onclick="saveChapter('${chapterId}')"
                            class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>Save Chapter
                    </button>
                </div>
            </form>
        </div>
    `;

    // Remove auto-save initialization
    // initializeChapterAutoSave(chapterId);
}

function loadLectureEditor(lectureId) {
    const lectureEditor = document.getElementById('lecture-editor');
    if (!lectureEditor) return;

    // Show loading state
    lectureEditor.innerHTML = `
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                <p class="text-gray-400">Loading lecture...</p>
            </div>
        </div>
    `;
    lectureEditor.classList.remove('hidden');

    // Get chapter ID from the lecture element
    const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
    const chapterElement = lectureElement?.closest('.chapter-item');
    const chapterId = chapterElement?.getAttribute('data-chapter-id');

    if (!chapterId) {
        console.error('Chapter ID not found for lecture:', lectureId);
        lectureEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                    <p class="text-red-400">Could not find chapter for this lecture</p>
                </div>
            </div>
        `;
        return;
    }

    // Fetch lecture data from backend
    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderLectureEditor(lectureId, data.data);
        } else {
            lectureEditor.innerHTML = `
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                        <p class="text-red-400">Failed to load lecture data</p>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading lecture:', error);
        lectureEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                    <p class="text-red-400">Network error loading lecture</p>
                </div>
            </div>
        `;
    });
}
