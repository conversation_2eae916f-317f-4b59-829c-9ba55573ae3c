<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class EnhancedFileUploadBrowserTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();

        // Create instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);

        // Create chapter
        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
        ]);

        // Create resource lecture
        $this->lecture = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'resource',
            'resources' => [],
        ]);
    }

    /** @test */
    public function enhanced_file_upload_interface_is_visible_and_functional()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertSee('Resource Content')
                    ->assertSee('Drop files here or click to browse')
                    ->assertVisible('#file-upload-zone-' . $this->lecture->id)
                    ->assertVisible('.upload-area')
                    ->assertVisible('.uploaded-files-container');
        });
    }

    /** @test */
    public function file_upload_zone_responds_to_hover_interactions()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->mouseover('.upload-area')
                    ->pause(500) // Allow for hover animations
                    ->assertPresent('.upload-area'); // Verify element is still present after hover
        });
    }

    /** @test */
    public function clicking_upload_area_triggers_file_input()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertVisible('#resource-files-' . $this->lecture->id)
                    ->assertAttribute('#resource-files-' . $this->lecture->id, 'multiple', 'true')
                    ->assertAttribute('#resource-files-' . $this->lecture->id, 'accept', '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.jpg,.jpeg,.png,.gif,.txt,.csv');
        });
    }

    /** @test */
    public function lecture_type_switching_initializes_file_upload()
    {
        // Create a video lecture first
        $videoLecture = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'video',
        ]);

        $this->browse(function (Browser $browser) use ($videoLecture) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $videoLecture->id . '"]')
                    ->click('[data-lecture-id="' . $videoLecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->select('select[name="type"]', 'resource')
                    ->pause(500) // Wait for content switching
                    ->assertVisible('#resource-content-' . $videoLecture->id)
                    ->assertVisible('#file-upload-zone-' . $videoLecture->id)
                    ->assertSee('Drop files here or click to browse');
        });
    }

    /** @test */
    public function progress_indicator_elements_are_present()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertPresent('#upload-progress-' . $this->lecture->id)
                    ->assertPresent('#upload-progress-bar-' . $this->lecture->id)
                    ->assertPresent('#upload-status-' . $this->lecture->id)
                    ->assertAttribute('#upload-progress-' . $this->lecture->id, 'class', 'upload-progress-container mt-4 hidden');
        });
    }

    /** @test */
    public function file_list_container_displays_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertVisible('#uploaded-files-' . $this->lecture->id)
                    ->assertSee('No files uploaded yet');
        });
    }

    /** @test */
    public function resource_description_field_is_present()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertVisible('textarea[name="resource_description"]')
                    ->assertAttribute('textarea[name="resource_description"]', 'placeholder', 'Describe what these resources contain...');
        });
    }

    /** @test */
    public function mobile_responsive_design_works_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->resize(375, 667) // iPhone SE dimensions
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertVisible('.upload-area')
                    ->assertVisible('.uploaded-files-container')
                    ->assertSee('Drop files here or click to browse');
        });
    }

    /** @test */
    public function tablet_responsive_design_works_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->resize(768, 1024) // iPad dimensions
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertVisible('.upload-area')
                    ->assertVisible('.uploaded-files-container')
                    ->assertSee('Drop files here or click to browse');
        });
    }

    /** @test */
    public function desktop_responsive_design_works_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->resize(1920, 1080) // Desktop dimensions
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertVisible('.upload-area')
                    ->assertVisible('.uploaded-files-container')
                    ->assertSee('Drop files here or click to browse');
        });
    }

    /** @test */
    public function css_animations_and_styles_are_loaded()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->assertSourceHas('course-builder-show.css')
                    ->assertSourceHas('course-builder-file-upload.js');
        });
    }

    /** @test */
    public function javascript_functions_are_available()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->instructor)
                    ->visit(route('course-builder.show', $this->course->slug))
                    ->waitFor('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->click('[data-lecture-id="' . $this->lecture->id . '"]')
                    ->waitFor('#lecture-editor')
                    ->script([
                        'return typeof initializeFileUpload === "function"',
                        'return typeof validateFile === "function"',
                        'return typeof uploadFiles === "function"',
                        'return typeof refreshFilesList === "function"',
                        'return typeof formatFileSize === "function"',
                        'return typeof getFileIcon === "function"',
                    ]);

            // Verify all functions exist
            $results = $browser->driver->executeScript('
                return [
                    typeof initializeFileUpload === "function",
                    typeof validateFile === "function", 
                    typeof uploadFiles === "function",
                    typeof refreshFilesList === "function",
                    typeof formatFileSize === "function",
                    typeof getFileIcon === "function"
                ];
            ');

            foreach ($results as $result) {
                $this->assertTrue($result, 'JavaScript function should be available');
            }
        });
    }
}
