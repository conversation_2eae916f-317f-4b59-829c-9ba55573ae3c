@extends('layouts.app')

@section('title', 'Course Builder - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-full px-4 sm:px-6 lg:px-8">
            <div class="py-4">
                <!-- Mobile/Tablet/Desktop Header Layout -->
                <div class="flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <!-- Mobile/Tablet: Separate rows for better space management -->
                    <div class="lg:hidden">
                        <!-- Mobile/Tablet Top Row: Back button and Menu Toggle -->
                        <div class="flex items-center justify-between mb-2">
                            <a href="{{ route('instructor.courses.index') }}"
                               class="text-gray-400 hover:text-white transition-colors text-sm flex items-center">
                                <i class="fas fa-arrow-left mr-1"></i>
                                <span>Back</span>
                            </a>
                            <button id="mobile-sidebar-toggle"
                                    class="text-gray-400 hover:text-white transition-colors p-2">
                                <i class="fas fa-bars text-lg"></i>
                            </button>
                        </div>
                        <!-- Mobile/Tablet Title Row: Full width for title -->
                        <div class="w-full">
                            <h1 class="text-lg md:text-xl font-bold text-white truncate pr-4">{{ $course->title }}</h1>
                        </div>
                    </div>

                    <!-- Desktop Layout: Traditional horizontal layout -->
                    <div class="hidden lg:flex lg:items-center lg:space-x-4">
                        <a href="{{ route('instructor.courses.index') }}"
                           class="text-gray-400 hover:text-white transition-colors text-base">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <span>Back to Courses</span>
                        </a>
                        <div class="h-6 w-px bg-gray-700"></div>
                        <h1 class="text-xl font-bold text-white">{{ $course->title }}</h1>
                    </div>

                    <!-- Action Buttons Row: Save Status and Publish Button -->
                    <div class="flex items-center justify-end space-x-2 md:space-x-3">
                        <!-- Save status indicator -->
                        <div id="save-status-indicator" class="hidden px-2 py-1 md:px-3 md:py-1 rounded-lg text-xs md:text-sm font-medium">
                            <i class="fas fa-check-circle mr-1 md:mr-2"></i>
                            <span id="save-status-text" class="hidden sm:inline">Changes saved</span>
                            <span class="sm:hidden">Saved</span>
                        </div>

                        <!-- Publish/Unpublish button -->
                        <button id="publish-toggle-btn"
                                class="px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors {{ $course->status === 'published' ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white' }}"
                                data-course-id="{{ $course->id }}"
                                data-current-status="{{ $course->status }}">
                            <i class="fas {{ $course->status === 'published' ? 'fa-eye' : 'fa-eye-slash' }} mr-1 md:mr-2"></i>
                            <span class="hidden sm:inline">{{ $course->status === 'published' ? 'Published' : 'Publish Course' }}</span>
                            <span class="sm:hidden">{{ $course->status === 'published' ? 'Live' : 'Publish' }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col lg:flex-row min-h-screen">
        <!-- Mobile/Tablet Sidebar Overlay -->
        <div id="mobile-sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

        <!-- Sidebar - Curriculum Panel -->
        <div id="curriculum-sidebar"
             class="fixed inset-y-0 left-0 z-50 w-80 bg-gray-900 border-r border-gray-800 overflow-y-auto transform -translate-x-full transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:w-1/3 lg:z-auto">
            <div class="p-4 lg:p-6">
                <!-- Mobile/Tablet Sidebar Header -->
                <div class="flex items-center justify-between mb-4 lg:hidden">
                    <h2 class="text-lg font-semibold text-white">Course Curriculum</h2>
                    <button id="mobile-sidebar-close" class="text-gray-400 hover:text-white transition-colors p-2">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- Desktop Sidebar Header -->
                <div class="hidden lg:flex items-center justify-between mb-6">
                    <h2 class="text-lg font-semibold text-white">Course Curriculum</h2>
                    <button id="add-chapter-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Chapter
                    </button>
                </div>

                <!-- Mobile/Tablet Add Chapter Button -->
                <div class="mb-4 lg:hidden">
                    <button id="add-chapter-btn-mobile"
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Chapter
                    </button>
                </div>

                <!-- Curriculum Tree -->
                <div id="curriculum-tree" class="space-y-2">
                    @forelse($course->chapters as $chapter)
                        <div class="chapter-item bg-gray-800 rounded-lg border border-gray-700" 
                             data-chapter-id="{{ $chapter->id }}"
                             data-chapter-index="{{ $loop->index }}">
                            <!-- Chapter Header -->
                            <div class="chapter-header p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                                 onclick="selectItem('chapter', '{{ $chapter->id }}')">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                            <i class="fas fa-grip-vertical"></i>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-folder text-yellow-500"></i>
                                            <span class="text-white font-medium">{{ $chapter->title }}</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs text-gray-400">{{ $chapter->lectures->count() }} lectures</span>
                                        <button class="text-gray-400 hover:text-white transition-colors"
                                                onclick="event.stopPropagation(); toggleChapter('{{ $chapter->id }}')">
                                            <i class="fas fa-chevron-down chapter-toggle"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Chapter Lectures -->
                            <div class="chapter-lectures pl-8 pb-2" id="chapter-lectures-{{ $chapter->id }}">
                                @foreach($chapter->lectures as $lecture)
                                    <div class="lecture-item p-3 hover:bg-gray-750 rounded cursor-pointer transition-colors"
                                         data-lecture-id="{{ $lecture->id }}"
                                         data-lecture-index="{{ $loop->index }}"
                                         onclick="selectItem('lecture', '{{ $lecture->id }}')">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                                    <i class="fas fa-grip-vertical text-xs"></i>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    @switch($lecture->type)
                                                        @case('video')
                                                            <i class="fas fa-play-circle text-blue-500"></i>
                                                            @break
                                                        @case('text')
                                                            <i class="fas fa-file-text text-green-500"></i>
                                                            @break
                                                        @case('quiz')
                                                            <i class="fas fa-question-circle text-purple-500"></i>
                                                            @break
                                                        @case('assignment')
                                                            <i class="fas fa-tasks text-orange-500"></i>
                                                            @break
                                                        @case('resource')
                                                            <i class="fas fa-download text-gray-500"></i>
                                                            @break
                                                        @default
                                                            <i class="fas fa-file text-gray-500"></i>
                                                    @endswitch
                                                    <span class="text-gray-300 text-sm">{{ $lecture->title }}</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                @if($lecture->duration_minutes)
                                                    <span class="text-xs text-gray-500">{{ $lecture->duration_minutes }}min</span>
                                                @endif
                                                @if($lecture->is_free_preview)
                                                    <span class="text-xs bg-green-600 text-white px-2 py-1 rounded">Preview</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Add Lecture Button -->
                                <div class="p-3">
                                    <button class="add-lecture-btn w-full text-left text-gray-400 hover:text-white transition-colors text-sm"
                                            data-chapter-id="{{ $chapter->id }}"
                                            onclick="addLecture('{{ $chapter->id }}')">
                                        <i class="fas fa-plus mr-2"></i>Add Lecture
                                    </button>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div id="empty-curriculum" class="text-center py-12">
                            <div class="text-gray-500 mb-4">
                                <i class="fas fa-folder-open text-4xl"></i>
                            </div>
                            <p class="text-gray-400 mb-4">No chapters yet</p>
                            <button id="add-first-chapter-btn" 
                                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Your First Chapter
                            </button>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Main Editor Panel -->
        <div class="flex-1 bg-black overflow-y-auto lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Course Overview Section -->
                <div id="course-overview" class="mb-6 md:mb-8">
                    <div class="bg-gradient-to-r from-gray-900 to-gray-800 border border-gray-700 rounded-lg p-4 md:p-6">
                        <!-- Mobile Layout -->
                        <div class="block md:hidden">
                            <div class="mb-4">
                                <h2 class="text-xl font-bold text-white mb-2">{{ $course->title }}</h2>
                                @if($course->subtitle)
                                    <p class="text-gray-300 mb-3 text-sm">{{ $course->subtitle }}</p>
                                @endif
                            </div>

                            <!-- Mobile Stats Grid -->
                            <div class="grid grid-cols-2 gap-3 mb-4 text-xs text-gray-400">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-tag"></i>
                                    <span class="truncate">{{ $course->category->name ?? 'No Category' }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-signal"></i>
                                    <span class="capitalize">{{ str_replace('_', ' ', $course->level) }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-book"></i>
                                    <span>{{ $course->chapters->count() }} Chapters</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-play-circle"></i>
                                    <span>{{ $course->lectures->count() }} Lectures</span>
                                </div>
                            </div>

                            <!-- Mobile Price and Edit Button -->
                            <div class="flex items-center justify-between">
                                <div>
                                    @if($course->price > 0)
                                        <span class="text-xl font-bold text-green-400">${{ number_format($course->price, 2) }}</span>
                                        <div class="text-xs text-gray-400">Paid Course</div>
                                    @else
                                        <span class="text-xl font-bold text-blue-400">FREE</span>
                                        <div class="text-xs text-gray-400">Free Course</div>
                                    @endif
                                </div>
                                <button onclick="selectItem('course', '{{ $course->id }}')"
                                        class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                            </div>
                        </div>

                        <!-- Desktop Layout -->
                        <div class="hidden md:block">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h2 class="text-2xl font-bold text-white mb-2">{{ $course->title }}</h2>
                                    @if($course->subtitle)
                                        <p class="text-gray-300 mb-3">{{ $course->subtitle }}</p>
                                    @endif
                                    <div class="flex items-center space-x-6 text-sm text-gray-400">
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-tag"></i>
                                            <span>{{ $course->category->name ?? 'No Category' }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-signal"></i>
                                            <span class="capitalize">{{ str_replace('_', ' ', $course->level) }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-book"></i>
                                            <span>{{ $course->chapters->count() }} Chapters</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-play-circle"></i>
                                            <span>{{ $course->lectures->count() }} Lectures</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="mb-2">
                                        @if($course->price > 0)
                                            <span class="text-2xl font-bold text-green-400">${{ number_format($course->price, 2) }}</span>
                                            <div class="text-xs text-gray-400">Paid Course</div>
                                        @else
                                            <span class="text-2xl font-bold text-blue-400">FREE</span>
                                            <div class="text-xs text-gray-400">Free Course</div>
                                        @endif
                                    </div>
                                    <button onclick="selectItem('course', '{{ $course->id }}')"
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-edit mr-2"></i>Edit Details
                                    </button>
                                </div>
                            </div>
                        </div>

                        @if($course->description)
                            <div class="border-t border-gray-700 pt-4">
                                <p class="text-gray-300 text-sm leading-relaxed">{{ Str::limit($course->description, 200) }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Welcome State -->
                <div id="welcome-state" class="{{ $course->chapters->count() > 0 ? 'hidden' : '' }}">
                    <div class="text-center py-16">
                        <div class="text-gray-600 mb-6">
                            <i class="fas fa-edit text-6xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4">Welcome to Course Builder</h3>
                        <p class="text-gray-400 mb-8 max-w-md mx-auto">
                            Create engaging course content with our intuitive builder.
                            Start by adding your first chapter from the sidebar.
                        </p>
                        <div class="space-y-4">
                            <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-left max-w-md mx-auto">
                                <h4 class="text-white font-medium mb-2">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    Pro Tips
                                </h4>
                                <ul class="text-gray-400 text-sm space-y-1">
                                    <li>• Organize content into logical chapters</li>
                                    <li>• Mix different content types for engagement</li>
                                    <li>• Use auto-save - no manual saving needed!</li>
                                    <li>• Drag and drop to reorder content</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Details Editor -->
                <div id="course-editor" class="hidden">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 md:p-6">
                        <h3 class="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">Course Details</h3>

                        <form id="course-details-form" class="space-y-4 md:space-y-6">
                            @csrf
                            <!-- Mobile: Stack all fields, Desktop: Two columns for title/subtitle -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Title <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="title" value="{{ $course->title }}"
                                           class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                           placeholder="Enter course title">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Subtitle
                                    </label>
                                    <input type="text" name="subtitle" value="{{ $course->subtitle }}"
                                           class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                           placeholder="Enter course subtitle">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" rows="4"
                                          class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                          placeholder="Describe what students will learn in this course">{{ $course->description }}</textarea>
                            </div>

                            <!-- Mobile: Stack all fields, Desktop: Three columns -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <select name="category_id"
                                            class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base">
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ $course->category_id == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Level <span class="text-red-500">*</span>
                                    </label>
                                    <select name="level"
                                            class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base">
                                        <option value="beginner" {{ $course->level == 'beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value="intermediate" {{ $course->level == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value="advanced" {{ $course->level == 'advanced' ? 'selected' : '' }}>Advanced</option>
                                        <option value="all_levels" {{ $course->level == 'all_levels' ? 'selected' : '' }}>All Levels</option>
                                    </select>
                                </div>

                                <div class="md:col-span-2 lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Price <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                                        <input type="number" name="price" value="{{ $course->price }}" step="0.01" min="0" max="999.99"
                                               class="w-full pl-8 pr-3 py-3 md:pr-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                               placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                                <button type="button" onclick="saveCourse()"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                                    <i class="fas fa-save mr-2"></i>Save Course Details
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Chapter Editor -->
                <div id="chapter-editor" class="hidden">
                    <!-- Chapter editor content will be loaded dynamically -->
                </div>

                <!-- Lecture Editor -->
                <div id="lecture-editor" class="hidden">
                    <!-- Lecture editor content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF Token for AJAX requests -->
<meta name="csrf-token" content="{{ csrf_token() }}">

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    const courseId = '{{ $course->slug }}';
    let currentSelection = { type: null, id: null };
    let autoSaveTimeouts = new Map();
    let isDragging = false;

    // Initialize the course builder
    initializeCourseBuilder();

    function initializeCourseBuilder() {
        // Initialize mobile navigation
        initializeMobileNavigation();

        // Initialize drag and drop
        initializeDragAndDrop();

        // Initialize auto-save
        initializeAutoSave();

        // Initialize event listeners
        initializeEventListeners();

        // Show course details by default if no chapters
        if (document.querySelectorAll('.chapter-item').length === 0) {
            selectItem('course', courseId);
        }
    }

    function initializeMobileNavigation() {
        const mobileToggle = document.getElementById('mobile-sidebar-toggle');
        const mobileClose = document.getElementById('mobile-sidebar-close');
        const sidebar = document.getElementById('curriculum-sidebar');
        const overlay = document.getElementById('mobile-sidebar-overlay');

        // Mobile sidebar toggle
        if (mobileToggle) {
            mobileToggle.addEventListener('click', function() {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            });
        }

        // Mobile sidebar close
        if (mobileClose) {
            mobileClose.addEventListener('click', closeMobileSidebar);
        }

        // Overlay click to close
        if (overlay) {
            overlay.addEventListener('click', closeMobileSidebar);
        }

        // Close sidebar on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !sidebar.classList.contains('-translate-x-full')) {
                closeMobileSidebar();
            }
        });

        // Auto-close sidebar on desktop resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) { // lg breakpoint
                closeMobileSidebar();
            }
        });

        function closeMobileSidebar() {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    }

    function initializeEventListeners() {
        // Add chapter buttons (desktop and mobile)
        document.getElementById('add-chapter-btn')?.addEventListener('click', addChapter);
        document.getElementById('add-chapter-btn-mobile')?.addEventListener('click', function() {
            addChapter();
            // Close mobile/tablet sidebar after adding chapter
            const sidebar = document.getElementById('curriculum-sidebar');
            const overlay = document.getElementById('mobile-sidebar-overlay');
            if (window.innerWidth < 1024) {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        });
        document.getElementById('add-first-chapter-btn')?.addEventListener('click', addChapter);

        // Publish toggle button
        document.getElementById('publish-toggle-btn')?.addEventListener('click', togglePublishStatus);

        // Prevent form submission
        document.getElementById('course-details-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
        });

        // Touch-friendly interactions for mobile
        if ('ontouchstart' in window) {
            // Add touch feedback for interactive elements
            document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.backgroundColor = 'rgba(127, 29, 29, 0.2)';
                });
                item.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });
            });
        }
    }

    function initializeDragAndDrop() {
        // Make chapters sortable
        const curriculumTree = document.getElementById('curriculum-tree');
        if (curriculumTree) {
            new Sortable(curriculumTree, {
                handle: '.drag-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onStart: function() {
                    isDragging = true;
                },
                onEnd: function(evt) {
                    isDragging = false;
                    if (evt.oldIndex !== evt.newIndex) {
                        reorderChapters();
                    }
                }
            });
        }

        // Make lectures sortable within each chapter
        document.querySelectorAll('.chapter-lectures').forEach(lecturesContainer => {
            new Sortable(lecturesContainer, {
                handle: '.drag-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                filter: '.add-lecture-btn',
                onStart: function() {
                    isDragging = true;
                },
                onEnd: function(evt) {
                    isDragging = false;
                    if (evt.oldIndex !== evt.newIndex) {
                        const chapterId = lecturesContainer.id.replace('chapter-lectures-', '');
                        reorderLectures(chapterId);
                    }
                }
            });
        });
    }

    function initializeAutoSave() {
        // Auto-save disabled - using manual save instead
        // initializeCourseAutoSave();
    }

    function initializeCourseAutoSave() {
        // Auto-save for course details
        const courseForm = document.getElementById('course-details-form');
        if (courseForm) {
            const inputs = courseForm.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                // Remove existing listeners to prevent duplicates
                input.removeEventListener('input', input._autoSaveHandler);
                input.removeEventListener('change', input._autoSaveHandler);

                const eventType = input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    scheduleAutoSave('course', courseId, () => autoSaveCourse());
                };

                // Store handler reference for removal
                input._autoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            });
        }
    }

    function scheduleAutoSave(type, id, saveFunction) {
        const key = `${type}-${id}`;

        // Clear existing timeout
        if (autoSaveTimeouts.has(key)) {
            clearTimeout(autoSaveTimeouts.get(key));
        }

        // Show saving indicator
        showSaveStatus('saving', 'Saving...');

        // Schedule new save
        const timeoutId = setTimeout(() => {
            saveFunction();
            autoSaveTimeouts.delete(key);
        }, 1000); // 1 second delay

        autoSaveTimeouts.set(key, timeoutId);
    }

    function showSaveStatus(status, message) {
        const indicator = document.getElementById('auto-save-indicator');
        const statusText = document.getElementById('save-status');

        if (!indicator || !statusText) return;

        // Remove existing status classes
        indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

        switch (status) {
            case 'saving':
                indicator.className += ' bg-blue-600 text-white';
                statusText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
                break;
            case 'saved':
                indicator.className += ' bg-green-600 text-white';
                statusText.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
                break;
            case 'error':
                indicator.className += ' bg-red-600 text-white';
                statusText.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
                break;
        }

        indicator.classList.remove('hidden');

        // Auto-hide success/error messages
        if (status !== 'saving') {
            setTimeout(() => {
                indicator.classList.add('hidden');
            }, 3000);
        }
    }

    function showFileUploadIndicator(lectureId, show) {
        const indicator = document.getElementById(`file-upload-indicator-${lectureId}`);
        if (!indicator) return;

        if (show) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }

    function selectItem(type, id) {
        if (isDragging) return; // Don't select during drag operations

        currentSelection = { type, id };

        // Update sidebar selection
        updateSidebarSelection(type, id);

        // Load content in main editor
        loadEditor(type, id);

        // Close mobile/tablet sidebar when selecting an item
        if (window.innerWidth < 1024) {
            const sidebar = document.getElementById('curriculum-sidebar');
            const overlay = document.getElementById('mobile-sidebar-overlay');
            if (sidebar && overlay) {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }
    }

    function updateSidebarSelection(type, id) {
        // Remove existing selections
        document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
            item.classList.remove('bg-red-900', 'border-red-600');
        });

        // Add selection to current item
        if (type === 'chapter') {
            const chapterItem = document.querySelector(`[data-chapter-id="${id}"]`);
            if (chapterItem) {
                chapterItem.classList.add('bg-red-900', 'border-red-600');
            }
        } else if (type === 'lecture') {
            const lectureItem = document.querySelector(`[data-lecture-id="${id}"]`);
            if (lectureItem) {
                lectureItem.classList.add('bg-red-900', 'border-red-600');
            }
        }
    }

    function loadEditor(type, id) {
        // Hide all editors
        document.getElementById('welcome-state')?.classList.add('hidden');
        document.getElementById('course-editor')?.classList.add('hidden');
        document.getElementById('chapter-editor')?.classList.add('hidden');
        document.getElementById('lecture-editor')?.classList.add('hidden');

        switch (type) {
            case 'course':
                document.getElementById('course-editor')?.classList.remove('hidden');
                // Auto-save disabled - using manual save instead
                // initializeCourseAutoSave();
                break;
            case 'chapter':
                loadChapterEditor(id);
                break;
            case 'lecture':
                loadLectureEditor(id);
                break;
        }
    }

    function loadChapterEditor(chapterId) {
        const chapterEditor = document.getElementById('chapter-editor');
        if (!chapterEditor) return;

        // Show loading state
        chapterEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                    <p class="text-gray-400">Loading chapter...</p>
                </div>
            </div>
        `;
        chapterEditor.classList.remove('hidden');

        // Fetch chapter data from backend
        fetch(`/instructor/courses/${courseId}/chapters/${chapterId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderChapterEditor(chapterId, data.data);
            } else {
                chapterEditor.innerHTML = `
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                            <p class="text-red-400">Failed to load chapter data</p>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading chapter:', error);
            chapterEditor.innerHTML = `
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                        <p class="text-red-400">Network error loading chapter</p>
                    </div>
                </div>
            `;
        });
    }

    function renderChapterEditor(chapterId, chapterData) {
        const chapterEditor = document.getElementById('chapter-editor');
        if (!chapterEditor) return;

        const learningObjectives = Array.isArray(chapterData.learning_objectives)
            ? chapterData.learning_objectives.join('\n')
            : '';

        chapterEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-white">Edit Chapter</h3>
                    <button onclick="deleteChapter('${chapterId}')"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete Chapter
                    </button>
                </div>

                <form id="chapter-form-${chapterId}" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Chapter Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" value="${chapterData.title || ''}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                               placeholder="Enter chapter title">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Chapter Description
                        </label>
                        <textarea name="description" rows="4"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                  placeholder="Describe what this chapter covers">${chapterData.description || ''}</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Learning Objectives
                        </label>
                        <textarea name="learning_objectives" rows="4"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                  placeholder="Enter learning objectives (one per line)">${learningObjectives}</textarea>
                        <p class="text-xs text-gray-400 mt-1">Enter each learning objective on a new line</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_published" ${chapterData.is_published ? 'checked' : ''}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Published</span>
                            </label>
                        </div>

                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_free_preview" ${chapterData.is_free_preview ? 'checked' : ''}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Free Preview</span>
                            </label>
                        </div>
                    </div>
                    <!-- Save Button -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                        <button type="button" onclick="saveChapter('${chapterId}')"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                            <i class="fas fa-save mr-2"></i>Save Chapter
                        </button>
                    </div>
                </form>
            </div>
        `;

        // Remove auto-save initialization
        // initializeChapterAutoSave(chapterId);
    }

    function initializeChapterAutoSave(chapterId) {
        const chapterForm = document.getElementById(`chapter-form-${chapterId}`);
        if (chapterForm) {
            const inputs = chapterForm.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                // Remove existing listeners to prevent duplicates
                input.removeEventListener('input', input._autoSaveHandler);
                input.removeEventListener('change', input._autoSaveHandler);

                const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    scheduleAutoSave('chapter', chapterId, () => autoSaveChapter(chapterId));
                };

                // Store handler reference for removal
                input._autoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            });
        }
    }

    function initializeLectureAutoSave(lectureId) {
        const lectureForm = document.getElementById(`lecture-form-${lectureId}`);
        if (!lectureForm) {
            console.error('Lecture form not found for auto-save initialization:', lectureId);
            return;
        }

        // Get all form inputs, including those in hidden content sections
        const inputs = lectureForm.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            // Remove existing listeners to prevent duplicates
            if (input._lectureAutoSaveHandler) {
                input.removeEventListener('input', input._lectureAutoSaveHandler);
                input.removeEventListener('change', input._lectureAutoSaveHandler);
            }

            // Special handling for file inputs
            if (input.type === 'file' && input.name === 'resource_file') {
                const fileHandler = function() {
                    if (input.files && input.files.length > 0) {
                        console.log('Resource file selected, triggering immediate upload');
                        console.log('Selected files:', Array.from(input.files).map(f => f.name));
                        showFileUploadIndicator(lectureId, true);
                        // Trigger immediate auto-save for file uploads
                        autoSaveLecture(lectureId);
                    }
                };
                input._lectureAutoSaveHandler = fileHandler;
                input.addEventListener('change', fileHandler);
            } else {
                const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    // Add a small delay to ensure the form state is updated
                    setTimeout(() => {
                        scheduleAutoSave('lecture', lectureId, () => autoSaveLecture(lectureId));
                    }, 50);
                };

                // Store handler reference for removal
                input._lectureAutoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            }
        });

        console.log(`Auto-save initialized for lecture ${lectureId} with ${inputs.length} form fields`);
    }

    function loadLectureEditor(lectureId) {
        const lectureEditor = document.getElementById('lecture-editor');
        if (!lectureEditor) return;

        // Show loading state
        lectureEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                    <span class="ml-3 text-gray-300">Loading lecture data...</span>
                </div>
            </div>
        `;

        // Get chapter ID from the lecture element
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterElement = lectureElement?.closest('.chapter-item');
        const chapterId = chapterElement?.getAttribute('data-chapter-id');

        if (!chapterId) {
            console.error('Chapter ID not found for lecture:', lectureId);
            return;
        }

        // Fetch lecture data from server
        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderLectureEditor(lectureId, data.lecture);
            } else {
                console.error('Failed to load lecture data:', data.message);
                lectureEditor.innerHTML = `
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="text-center py-8">
                            <div class="text-red-400 mb-2">
                                <i class="fas fa-exclamation-triangle text-2xl"></i>
                            </div>
                            <p class="text-gray-300">Failed to load lecture data</p>
                            <p class="text-sm text-gray-400">${data.message || 'Unknown error'}</p>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading lecture data:', error);
            lectureEditor.innerHTML = `
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <div class="text-center py-8">
                        <div class="text-red-400 mb-2">
                            <i class="fas fa-exclamation-triangle text-2xl"></i>
                        </div>
                        <p class="text-gray-300">Network error loading lecture data</p>
                    </div>
                </div>
            `;
        });
    }

    function renderLectureEditor(lectureId, lecture) {
        const lectureEditor = document.getElementById('lecture-editor');
        if (!lectureEditor) return;

        // Extract data from lecture object for form population
        const lectureTitle = lecture.title || '';
        const lectureType = lecture.type || 'text';
        const lectureDescription = lecture.description || '';
        const videoUrl = lecture.video_url || '';
        const durationMinutes = lecture.duration_minutes || '';
        const content = lecture.content || '';
        const estimatedCompletionMinutes = lecture.estimated_completion_minutes || '';
        const quizPassingScore = lecture.quiz_passing_score || 70;
        const quizAllowRetakes = lecture.quiz_allow_retakes ? 'checked' : '';
        const isPublished = lecture.is_published ? 'checked' : '';
        const isFreePreview = lecture.is_free_preview ? 'checked' : '';
        const isMandatory = lecture.is_mandatory ? 'checked' : '';

        // Extract type-specific data from JSON fields
        const quizInstructions = lecture.quiz_data?.instructions || '';
        const assignmentInstructions = lecture.attachments?.instructions || '';
        const assignmentMaxPoints = lecture.attachments?.max_points || 100;
        const assignmentDueDate = lecture.attachments?.due_date || '';
        const resourceDescription = lecture.resources?.description || '';
        const resourceUrl = lecture.resources?.url || '';
        const resourceFiles = lecture.resources?.files || [];
        
        // Debug logging
        console.log('Lecture resources data:', lecture.resources);
        console.log('Resource files:', resourceFiles);

        lectureEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-white">Edit Lecture</h3>
                    <button onclick="deleteLecture('${lectureId}')"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete Lecture
                    </button>
                </div>

                <form id="lecture-form-${lectureId}" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Lecture Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="title" value="${lectureTitle}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                   placeholder="Enter lecture title">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Lecture Type <span class="text-red-500">*</span>
                            </label>
                            <select name="type" onchange="toggleLectureContent('${lectureId}', this.value)"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500">
                                <option value="video" ${lectureType === 'video' ? 'selected' : ''}>Video</option>
                                <option value="text" ${lectureType === 'text' ? 'selected' : ''}>Text/Article</option>
                                <option value="quiz" ${lectureType === 'quiz' ? 'selected' : ''}>Quiz</option>
                                <option value="assignment" ${lectureType === 'assignment' ? 'selected' : ''}>Assignment</option>
                                <option value="resource" ${lectureType === 'resource' ? 'selected' : ''}>Resource</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Description
                        </label>
                        <textarea name="description" rows="3"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                  placeholder="Describe what students will learn in this lecture">${lectureDescription}</textarea>
                    </div>

                    <!-- Video Content -->
                    <div id="video-content-${lectureId}" class="lecture-content ${lectureType === 'video' ? '' : 'hidden'}">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Video URL
                                </label>
                                <input type="url" name="video_url" value="${videoUrl}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                       placeholder="https://youtube.com/watch?v=...">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Duration (minutes)
                                </label>
                                <input type="number" name="duration_minutes" min="0" max="1440" value="${durationMinutes}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                       placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Text Content -->
                    <div id="text-content-${lectureId}" class="lecture-content ${lectureType === 'text' ? '' : 'hidden'}">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Content
                            </label>
                            <textarea name="content" rows="8"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Write your lesson content here...">${content}</textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Estimated Reading Time (minutes)
                            </label>
                            <input type="number" name="estimated_completion_minutes" min="0" max="1440" value="${estimatedCompletionMinutes}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                   placeholder="0">
                        </div>
                    </div>

                    <!-- Quiz Content -->
                    <div id="quiz-content-${lectureId}" class="lecture-content ${lectureType === 'quiz' ? '' : 'hidden'}">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Passing Score (%)
                                </label>
                                <input type="number" name="quiz_passing_score" min="0" max="100" value="${quizPassingScore}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            </div>

                            <div>
                                <label class="flex items-center space-x-3 mt-8">
                                    <input type="checkbox" name="quiz_allow_retakes" ${quizAllowRetakes}
                                           class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                    <span class="text-gray-300">Allow Retakes</span>
                                </label>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Quiz Instructions
                            </label>
                            <textarea name="quiz_instructions" rows="4"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Instructions for students taking this quiz...">${quizInstructions}</textarea>
                        </div>
                    </div>

                    <!-- Assignment Content -->
                    <div id="assignment-content-${lectureId}" class="lecture-content ${lectureType === 'assignment' ? '' : 'hidden'}">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Assignment Instructions
                            </label>
                            <textarea name="assignment_instructions" rows="8"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Detailed instructions for the assignment...">${assignmentInstructions}</textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Maximum Points
                                </label>
                                <input type="number" name="assignment_max_points" min="0" max="1000" value="${assignmentMaxPoints}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Due Date (optional)
                                </label>
                                <input type="datetime-local" name="assignment_due_date" value="${assignmentDueDate}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            </div>
                        </div>
                    </div>

                    <!-- Resource Content -->
                    <div id="resource-content-${lectureId}" class="lecture-content ${lectureType === 'resource' ? '' : 'hidden'}">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Resource Description
                            </label>
                            <textarea name="resource_description" rows="4"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Describe what students will find in this resource...">${resourceDescription}</textarea>
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Resource URL (optional)
                            </label>
                            <input type="url" name="resource_url" value="${resourceUrl}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                   placeholder="https://example.com/resource">
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                File Upload (optional)
                            </label>
                            <div class="relative">
                                <input type="file" name="resource_file" multiple
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar">
                                <!-- Local upload indicator -->
                                <div id="file-upload-indicator-${lectureId}" class="hidden absolute top-2 right-2 px-3 py-1 bg-blue-600 text-white text-xs rounded-lg flex items-center">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    <span>Uploading...</span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-400 mt-1">Supported formats: PDF, Word, Excel, PowerPoint, ZIP, RAR</p>
                            
                            ${resourceFiles.length > 0 ? `
                                <div class="mt-4 p-4 bg-gray-800 border border-gray-700 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-300 mb-3">Uploaded Files:</h4>
                                    <div class="space-y-2">
                                        ${resourceFiles.map(file => `
                                            <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                                                <div class="flex items-center space-x-3">
                                                    <i class="fas fa-file text-blue-400"></i>
                                                    <div>
                                                        <p class="text-sm text-white font-medium">${file.name}</p>
                                                        <p class="text-xs text-gray-400">${(file.file_size / 1024 / 1024).toFixed(2)} MB</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <a href="/instructor/files/resources/view?path=${encodeURIComponent(file.file_path)}" 
   target="_blank"
   class="text-blue-400 hover:text-blue-300 text-sm">
    <i class="fas fa-eye mr-1"></i>View
</a>
<a href="/instructor/files/resources/download?path=${encodeURIComponent(file.file_path)}" 
   class="text-green-400 hover:text-green-300 text-sm">
    <i class="fas fa-download mr-1"></i>Download
</a>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_published" ${isPublished}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Published</span>
                            </label>
                        </div>

                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_free_preview" ${isFreePreview}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Free Preview</span>
                            </label>
                        </div>

                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_mandatory" ${isMandatory}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Mandatory</span>
                            </label>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                        <button type="button" onclick="saveLecture('${lectureId}')"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                            <i class="fas fa-save mr-2"></i>Save Lecture
                        </button>
                    </div>
                </form>
            </div>
        `;

        lectureEditor.classList.remove('hidden');

        // Remove auto-save initialization
        // initializeLectureAutoSave(lectureId);
    }

    function toggleLectureContent(lectureId, type) {
        console.log(`Toggling lecture content for lecture ${lectureId} to type: ${type}`);

        // Hide all content sections
        const contentSections = document.querySelectorAll(`#lecture-form-${lectureId} .lecture-content`);
        console.log(`Found ${contentSections.length} content sections to hide`);
        contentSections.forEach(section => {
            section.classList.add('hidden');
        });

        // Show selected content section
        const targetSection = document.getElementById(`${type}-content-${lectureId}`);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            console.log(`Showed content section for type: ${type}`);
        } else {
            console.warn(`Target section not found: ${type}-content-${lectureId}`);
        }

        // Re-initialize auto-save listeners for all form fields after content toggle
        // This ensures that newly visible type-specific fields have auto-save functionality
        console.log('Re-initializing auto-save listeners after content toggle');
        initializeLectureAutoSave(lectureId);
    }

    // Save functions
    function saveCourse() {
        const form = document.getElementById('course-details-form');
        if (!form) return;

        // Show saving status
        showSaveStatus('saving', 'Saving course details...');

        const formData = new FormData(form);

        fetch(`/instructor/course-builder/${courseId}/auto-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Course details saved successfully!');
                // Update course overview section
                updateCourseOverview(data.data);
            } else {
                showSaveStatus('error', data.message || 'Save failed');
            }
        })
        .catch(error => {
            console.error('Save error:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function autoSaveCourse() {
        // Auto-save functionality disabled - use manual save instead
        console.log('Auto-save disabled for course');
    }

    function updateCourseOverview(courseData) {
        // Update course title in overview
        const overviewTitle = document.querySelector('#course-overview h2');
        if (overviewTitle && courseData.title) {
            overviewTitle.textContent = courseData.title;
        }

        // Update course subtitle
        const overviewSubtitle = document.querySelector('#course-overview p');
        if (overviewSubtitle && courseData.subtitle) {
            overviewSubtitle.textContent = courseData.subtitle;
        }

        // Update pricing display
        const priceDisplay = document.querySelector('#course-overview .text-2xl');
        if (priceDisplay && courseData.price !== undefined) {
            if (courseData.price > 0) {
                priceDisplay.textContent = `$${parseFloat(courseData.price).toFixed(2)}`;
                priceDisplay.className = 'text-2xl font-bold text-green-400';
                const priceLabel = priceDisplay.nextElementSibling;
                if (priceLabel) priceLabel.textContent = 'Paid Course';
            } else {
                priceDisplay.textContent = 'FREE';
                priceDisplay.className = 'text-2xl font-bold text-blue-400';
                const priceLabel = priceDisplay.nextElementSibling;
                if (priceLabel) priceLabel.textContent = 'Free Course';
            }
        }

        // Update header title
        const headerTitle = document.querySelector('h1');
        if (headerTitle && courseData.title) {
            headerTitle.textContent = courseData.title;
        }
    }

    function saveChapter(chapterId) {
        const form = document.getElementById(`chapter-form-${chapterId}`);
        if (!form) return;

        // Show saving status
        showSaveStatus('saving', 'Saving chapter...');

        const formData = new FormData(form);

        // Convert learning objectives from string to array
        const learningObjectivesTextarea = form.querySelector('textarea[name="learning_objectives"]');
        if (learningObjectivesTextarea && learningObjectivesTextarea.value.trim()) {
            const objectives = learningObjectivesTextarea.value
                .split('\n')
                .map(obj => obj.trim())
                .filter(obj => obj.length > 0);

            // Remove the original field and add array values
            formData.delete('learning_objectives');
            objectives.forEach((objective, index) => {
                formData.append(`learning_objectives[${index}]`, objective);
            });
        }

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/auto-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Chapter saved successfully!');
                // Update sidebar title if changed
                const titleInput = form.querySelector('input[name="title"]');
                if (titleInput) {
                    const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
                    const titleSpan = chapterElement?.querySelector('.chapter-header span');
                    if (titleSpan) {
                        titleSpan.textContent = titleInput.value;
                    }
                }
            } else {
                showSaveStatus('error', data.message || 'Save failed');
            }
        })
        .catch(error => {
            console.error('Save error:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function autoSaveChapter(chapterId) {
        // Auto-save functionality disabled - use manual save instead
        console.log('Auto-save disabled for chapter:', chapterId);
    }

    function saveLecture(lectureId) {
        const form = document.getElementById(`lecture-form-${lectureId}`);
        if (!form) {
            console.error('Lecture form not found:', lectureId);
            return;
        }

        // Get chapter ID from the lecture element
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterElement = lectureElement?.closest('.chapter-item');
        const chapterId = chapterElement?.getAttribute('data-chapter-id');

        if (!chapterId) {
            console.error('Chapter ID not found for lecture:', lectureId);
            return;
        }

        // Show saving status
        showSaveStatus('saving', 'Saving lecture...');

        // Create FormData and ensure all fields are captured, including those in hidden sections
        const formData = new FormData();

        // Get all form inputs, including those in hidden content sections
        const allInputs = form.querySelectorAll('input, textarea, select');

        allInputs.forEach(input => {
            if (input.name) {
                if (input.type === 'checkbox') {
                    // Handle checkboxes - send as '1' or '0'
                    formData.append(input.name, input.checked ? '1' : '0');
                } else if (input.type === 'radio') {
                    // Handle radio buttons - only send if checked
                    if (input.checked) {
                        formData.append(input.name, input.value);
                    }
                } else if (input.type === 'file') {
                    // Handle file inputs - append actual files
                    if (input.files && input.files.length > 0) {
                        console.log(`Processing file input: ${input.name}`, {
                            multiple: input.multiple,
                            fileCount: input.files.length
                        });
                        
                        if (input.multiple) {
                            // For multiple files, append each with the same name (Laravel will handle as array)
                            Array.from(input.files).forEach((file, index) => {
                                console.log(`Appending file [${index}]:`, file.name);
                                formData.append(`${input.name}[]`, file);
                            });
                        } else {
                            // Handle single file
                            console.log(`Appending single file:`, input.files[0].name);
                            formData.append(input.name, input.files[0]);
                        }
                    } else {
                        console.log(`File input ${input.name} has no files selected`);
                    }
                } else {
                    // Handle all other input types
                    formData.append(input.name, input.value || '');
                }
            }
        });

        // Log form data for debugging
        console.log('Saving lecture data:', {
            lectureId: lectureId,
            chapterId: chapterId,
            formFields: Array.from(formData.entries()).map(([key, value]) => {
                if (value instanceof File) {
                    return [key, {
                        type: 'File',
                        name: value.name,
                        size: value.size,
                        mimeType: value.type,
                        constructor: value.constructor.name,
                        lastModified: value.lastModified
                    }];
                }
                return [key, value];
            })
        });
        
        console.log('Sending FormData to URL:', `/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/auto-save`);

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/auto-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Save response status:', response.status);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('Save HTTP error:', response.status, response.statusText, text);
                    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            // Hide file upload indicator
            showFileUploadIndicator(lectureId, false);
            
            if (data.success) {
                showSaveStatus('saved', 'Lecture saved successfully!');
                console.log('Lecture save successful:', data);

                // Update sidebar title and icon if changed
                const titleInput = form.querySelector('input[name="title"]');
                const typeSelect = form.querySelector('select[name="type"]');
                if (titleInput && lectureElement) {
                    const titleSpan = lectureElement.querySelector('span');
                    if (titleSpan) {
                        titleSpan.textContent = titleInput.value;
                    }

                    // Update icon based on type
                    if (typeSelect) {
                        const iconElement = lectureElement.querySelector('i');
                        if (iconElement) {
                            iconElement.className = getLectureIcon(typeSelect.value);
                        }
                    }
                }
            } else {
                console.error('Save failed:', data);
                showSaveStatus('error', data.message || 'Save failed');

                // Log detailed error information for debugging
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                    Object.keys(data.errors).forEach(field => {
                        console.error(`Field ${field}:`, data.errors[field]);
                    });
                }
            }
        })
        .catch(error => {
            // Hide file upload indicator
            showFileUploadIndicator(lectureId, false);
            
            console.error('Save network/fetch error:', error);
            showSaveStatus('error', 'Network error - please check your connection');
        });
    }

    function autoSaveLecture(lectureId) {
        // Auto-save functionality disabled - use manual save instead
        console.log('Auto-save disabled for lecture:', lectureId);
    }

    function getLectureIcon(type) {
        const icons = {
            'video': 'fas fa-play-circle text-blue-500',
            'text': 'fas fa-file-text text-green-500',
            'quiz': 'fas fa-question-circle text-purple-500',
            'assignment': 'fas fa-tasks text-orange-500',
            'resource': 'fas fa-download text-gray-500'
        };
        return icons[type] || 'fas fa-file text-gray-500';
    }

    // CRUD operations
    function addChapter() {
        const formData = new FormData();
        formData.append('title', 'New Chapter');
        formData.append('description', '');
        formData.append('is_free_preview', '0');

        fetch(`/instructor/course-builder/${courseId}/chapters`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addChapterToSidebar(data.data);
                selectItem('chapter', data.data.id);
                showSaveStatus('saved', 'Chapter created');

                // Hide empty state
                document.getElementById('empty-curriculum')?.classList.add('hidden');
                document.getElementById('welcome-state')?.classList.add('hidden');
            } else {
                showSaveStatus('error', data.message || 'Failed to create chapter');
            }
        })
        .catch(error => {
            console.error('Error creating chapter:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function addLecture(chapterId) {
        const formData = new FormData();
        formData.append('title', 'New Lecture');
        formData.append('description', '');
        formData.append('type', 'text');
        formData.append('is_free_preview', '0');
        formData.append('is_mandatory', '1');

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLectureToSidebar(chapterId, data.data);
                selectItem('lecture', data.data.id);
                showSaveStatus('saved', 'Lecture created');
            } else {
                showSaveStatus('error', data.message || 'Failed to create lecture');
            }
        })
        .catch(error => {
            console.error('Error creating lecture:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function deleteChapter(chapterId) {
        if (!confirm('Are you sure you want to delete this chapter? This will also delete all lectures in this chapter.')) {
            return;
        }

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from sidebar
                const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
                if (chapterElement) {
                    chapterElement.remove();
                }

                // Show welcome state if no chapters left
                if (document.querySelectorAll('.chapter-item').length === 0) {
                    document.getElementById('welcome-state')?.classList.remove('hidden');
                    document.getElementById('empty-curriculum')?.classList.remove('hidden');
                    document.getElementById('chapter-editor')?.classList.add('hidden');
                }

                showSaveStatus('saved', 'Chapter deleted');
            } else {
                showSaveStatus('error', data.message || 'Failed to delete chapter');
            }
        })
        .catch(error => {
            console.error('Error deleting chapter:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function deleteLecture(lectureId) {
        if (!confirm('Are you sure you want to delete this lecture?')) {
            return;
        }

        // Get chapter ID from the lecture element
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterElement = lectureElement?.closest('.chapter-item');
        const chapterId = chapterElement?.getAttribute('data-chapter-id');

        if (!chapterId) return;

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from sidebar
                if (lectureElement) {
                    lectureElement.remove();
                }

                // Update lecture count
                updateChapterLectureCount(chapterId);

                // Hide lecture editor
                document.getElementById('lecture-editor')?.classList.add('hidden');

                showSaveStatus('saved', 'Lecture deleted');
            } else {
                showSaveStatus('error', data.message || 'Failed to delete lecture');
            }
        })
        .catch(error => {
            console.error('Error deleting lecture:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function reorderChapters() {
        const chapterElements = document.querySelectorAll('.chapter-item');
        const chapterIds = Array.from(chapterElements).map(el => el.getAttribute('data-chapter-id'));

        fetch(`/instructor/course-builder/${courseId}/chapters/reorder`, {
            method: 'POST',
            body: JSON.stringify({ chapter_ids: chapterIds }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Chapters reordered');
            } else {
                showSaveStatus('error', data.message || 'Failed to reorder chapters');
            }
        })
        .catch(error => {
            console.error('Error reordering chapters:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function reorderLectures(chapterId) {
        const lectureElements = document.querySelectorAll(`#chapter-lectures-${chapterId} .lecture-item`);
        const lectureIds = Array.from(lectureElements).map(el => el.getAttribute('data-lecture-id'));

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/reorder`, {
            method: 'POST',
            body: JSON.stringify({ lecture_ids: lectureIds }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Lectures reordered');
            } else {
                showSaveStatus('error', data.message || 'Failed to reorder lectures');
            }
        })
        .catch(error => {
            console.error('Error reordering lectures:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function togglePublishStatus() {
        const button = document.getElementById('publish-toggle-btn');
        const currentStatus = button.getAttribute('data-current-status');

        fetch(`/instructor/course-builder/${courseId}/toggle-publish`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update button
                const newStatus = data.status;
                button.setAttribute('data-current-status', newStatus);

                if (newStatus === 'published') {
                    button.className = 'px-4 py-2 rounded-lg font-medium transition-colors bg-green-600 hover:bg-green-700 text-white';
                    button.innerHTML = '<i class="fas fa-eye mr-2"></i>Published';
                } else {
                    button.className = 'px-4 py-2 rounded-lg font-medium transition-colors bg-red-600 hover:bg-red-700 text-white';
                    button.innerHTML = '<i class="fas fa-eye-slash mr-2"></i>Publish Course';
                }

                showSaveStatus('saved', data.message);
            } else {
                showSaveStatus('error', data.message || 'Failed to update course status');
            }
        })
        .catch(error => {
            console.error('Error toggling publish status:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    // Helper functions for DOM manipulation
    function addChapterToSidebar(chapter) {
        const curriculumTree = document.getElementById('curriculum-tree');
        const emptyState = document.getElementById('empty-curriculum');

        if (emptyState) {
            emptyState.remove();
        }

        const chapterHtml = `
            <div class="chapter-item bg-gray-800 rounded-lg border border-gray-700"
                 data-chapter-id="${chapter.id}"
                 data-chapter-index="0">
                <!-- Chapter Header -->
                <div class="chapter-header p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                     onclick="selectItem('chapter', '${chapter.id}')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                <i class="fas fa-grip-vertical"></i>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-folder text-yellow-500"></i>
                                <span class="text-white font-medium">${chapter.title}</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-400">0 lectures</span>
                            <button class="text-gray-400 hover:text-white transition-colors"
                                    onclick="event.stopPropagation(); toggleChapter('${chapter.id}')">
                                <i class="fas fa-chevron-down chapter-toggle"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Chapter Lectures -->
                <div class="chapter-lectures pl-8 pb-2" id="chapter-lectures-${chapter.id}">
                    <!-- Add Lecture Button -->
                    <div class="p-3">
                        <button class="add-lecture-btn w-full text-left text-gray-400 hover:text-white transition-colors text-sm"
                                data-chapter-id="${chapter.id}"
                                onclick="addLecture('${chapter.id}')">
                            <i class="fas fa-plus mr-2"></i>Add Lecture
                        </button>
                    </div>
                </div>
            </div>
        `;

        curriculumTree.insertAdjacentHTML('beforeend', chapterHtml);

        // Initialize drag and drop for the new chapter
        initializeDragAndDrop();
    }

    function addLectureToSidebar(chapterId, lecture) {
        const lecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
        const addButton = lecturesContainer.querySelector('.add-lecture-btn').parentElement;

        const lectureHtml = `
            <div class="lecture-item p-3 hover:bg-gray-750 rounded cursor-pointer transition-colors"
                 data-lecture-id="${lecture.id}"
                 data-lecture-index="0"
                 onclick="selectItem('lecture', '${lecture.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                            <i class="fas fa-grip-vertical text-xs"></i>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="${getLectureIcon(lecture.type)}"></i>
                            <span class="text-gray-300 text-sm">${lecture.title}</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        ${lecture.duration_minutes ? `<span class="text-xs text-gray-500">${lecture.duration_minutes}min</span>` : ''}
                        ${lecture.is_free_preview ? '<span class="text-xs bg-green-600 text-white px-2 py-1 rounded">Preview</span>' : ''}
                    </div>
                </div>
            </div>
        `;

        addButton.insertAdjacentHTML('beforebegin', lectureHtml);

        // Update chapter lecture count
        updateChapterLectureCount(chapterId);

        // Initialize drag and drop for the new lecture
        initializeDragAndDrop();
    }

    function updateChapterLectureCount(chapterId) {
        const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
        const lectureCount = document.querySelectorAll(`#chapter-lectures-${chapterId} .lecture-item`).length;
        const countSpan = chapterElement?.querySelector('.text-xs.text-gray-400');

        if (countSpan) {
            countSpan.textContent = `${lectureCount} lecture${lectureCount !== 1 ? 's' : ''}`;
        }
    }

    function toggleChapter(chapterId) {
        const lecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
        const toggleIcon = document.querySelector(`[data-chapter-id="${chapterId}"] .chapter-toggle`);

        if (lecturesContainer && toggleIcon) {
            if (lecturesContainer.style.display === 'none') {
                lecturesContainer.style.display = 'block';
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-down');
            } else {
                lecturesContainer.style.display = 'none';
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-right');
            }
        }
    }

    // Global functions for onclick handlers
    window.selectItem = selectItem;
    window.addLecture = addLecture;
    window.deleteChapter = deleteChapter;
    window.deleteLecture = deleteLecture;
    window.toggleChapter = toggleChapter;
    window.toggleLectureContent = toggleLectureContent;
    window.saveCourse = saveCourse;
    window.saveChapter = saveChapter;
    window.saveLecture = saveLecture;
});
</script>

<style>
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    transform: scale(1.02);
}

.sortable-drag {
    transform: rotate(5deg);
}

.lecture-content.hidden {
    display: none !important;
}

.auto-save-indicator {
    transition: all 0.3s ease;
}

.chapter-item.bg-red-900 {
    background-color: rgba(127, 29, 29, 0.5) !important;
}

.lecture-item.bg-red-900 {
    background-color: rgba(127, 29, 29, 0.3) !important;
}

/* Mobile/Tablet-specific styles */
@media (max-width: 1023px) {
    /* Ensure mobile sidebar is properly positioned */
    #curriculum-sidebar {
        top: 0;
        bottom: 0;
        height: 100vh;
        max-height: 100vh;
    }

    /* Mobile touch targets */
    .chapter-item, .lecture-item {
        min-height: 48px; /* Minimum touch target size */
    }

    /* Mobile button sizing */
    button {
        min-height: 44px; /* iOS recommended touch target */
        min-width: 44px;
    }

    /* Mobile form inputs */
    input, textarea, select {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 44px;
    }

    /* Mobile spacing adjustments */
    .chapter-header {
        padding: 1rem !important;
    }

    .lecture-item {
        padding: 0.75rem !important;
    }

    /* Mobile text sizing */
    .chapter-item .text-white {
        font-size: 0.95rem;
    }

    .lecture-item .text-gray-300 {
        font-size: 0.875rem;
    }

    /* Hide drag handles on mobile for cleaner look */
    .drag-handle {
        display: none;
    }

    /* Mobile-friendly scrolling */
    .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
    }
}

/* Note: Tablets now use the same mobile layout (sidebar hidden) for better content space */

/* Smooth transitions for sidebar */
#curriculum-sidebar {
    transition: transform 0.3s ease-in-out;
}

/* Prevent body scroll when mobile sidebar is open */
body.overflow-hidden {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* Mobile overlay styling */
#mobile-sidebar-overlay {
    backdrop-filter: blur(2px);
}

/* Touch feedback */
.touch-feedback {
    background-color: rgba(127, 29, 29, 0.2);
    transition: background-color 0.15s ease;
}
</style>
@endpush
@endsection
