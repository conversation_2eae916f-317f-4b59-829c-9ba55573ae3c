<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EnhancedFileUploadTest extends TestCase
{
    use RefreshDatabase;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();

        // Create instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);

        // Create chapter
        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
        ]);

        // Create resource lecture
        $this->lecture = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'resource',
            'resources' => [],
        ]);

        // Set up fake storage
        Storage::fake('local');
    }

    /** @test */
    public function instructor_can_upload_multiple_resource_files()
    {
        $this->actingAs($this->instructor);

        // Create test files
        $files = [
            UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf'),
            UploadedFile::fake()->create('presentation.pptx', 2048, 'application/vnd.openxmlformats-officedocument.presentationml.presentation'),
            UploadedFile::fake()->image('image.jpg', 800, 600)->size(500),
        ];

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => $files,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Files uploaded successfully',
                ])
                ->assertJsonStructure([
                    'data' => [
                        'files' => [
                            '*' => [
                                'id',
                                'name',
                                'original_name',
                                'file_path',
                                'file_size',
                                'file_type',
                                'uploaded_at',
                                'download_url',
                            ]
                        ],
                        'total_files'
                    ]
                ]);

        // Verify files were stored
        $this->lecture->refresh();
        $this->assertCount(3, $this->lecture->resources);

        // Verify files exist in storage
        foreach ($this->lecture->resources as $resource) {
            Storage::disk('local')->assertExists($resource['file_path']);
        }
    }

    /** @test */
    public function file_upload_validates_file_types()
    {
        $this->actingAs($this->instructor);

        // Create invalid file type
        $invalidFile = UploadedFile::fake()->create('malicious.exe', 1024, 'application/x-executable');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$invalidFile],
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resource_files.0']);
    }

    /** @test */
    public function file_upload_validates_file_size()
    {
        $this->actingAs($this->instructor);

        // Create oversized file (52MB > 50MB limit)
        $oversizedFile = UploadedFile::fake()->create('large.pdf', 52 * 1024, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$oversizedFile],
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resource_files.0']);
    }

    /** @test */
    public function file_upload_limits_number_of_files()
    {
        $this->actingAs($this->instructor);

        // Create 11 files (exceeds limit of 10)
        $files = [];
        for ($i = 0; $i < 11; $i++) {
            $files[] = UploadedFile::fake()->create("file{$i}.pdf", 100, 'application/pdf');
        }

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => $files,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resource_files']);
    }

    /** @test */
    public function only_course_instructor_can_upload_files()
    {
        // Create different instructor
        $otherInstructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $this->actingAs($otherInstructor);

        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$file],
        ]);

        $response->assertStatus(403);
    }

    /** @test */
    public function files_are_stored_in_correct_directory_structure()
    {
        $this->actingAs($this->instructor);

        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$file],
        ]);

        $response->assertStatus(200);

        $this->lecture->refresh();
        $uploadedFile = $this->lecture->resources[0];

        // Verify directory structure
        $expectedPath = "private/courses/{$this->instructor->id}/{$this->course->id}/materials/resources";
        $this->assertStringStartsWith($expectedPath, $uploadedFile['file_path']);

        // Verify file exists
        Storage::disk('local')->assertExists($uploadedFile['file_path']);
    }

    /** @test */
    public function uploaded_files_merge_with_existing_resources()
    {
        $this->actingAs($this->instructor);

        // Add existing resources to lecture
        $existingResources = [
            [
                'id' => 'existing-1',
                'name' => 'existing-file.pdf',
                'file_path' => 'existing/path/file.pdf',
                'file_size' => 1024,
                'file_type' => 'application/pdf',
                'uploaded_at' => now()->toISOString(),
            ]
        ];

        $this->lecture->update(['resources' => $existingResources]);

        // Upload new file
        $newFile = UploadedFile::fake()->create('new-document.pdf', 2048, 'application/pdf');

        $response = $this->postJson(route('course-builder.lectures.upload-resources', [
            'course' => $this->course->slug,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
        ]), [
            'resource_files' => [$newFile],
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'data' => [
                        'total_files' => 2
                    ]
                ]);

        // Verify both files exist in resources
        $this->lecture->refresh();
        $this->assertCount(2, $this->lecture->resources);
        
        // Verify existing file is preserved
        $this->assertEquals('existing-file.pdf', $this->lecture->resources[0]['name']);
        
        // Verify new file is added
        $this->assertEquals('new-document.pdf', $this->lecture->resources[1]['name']);
    }
}
