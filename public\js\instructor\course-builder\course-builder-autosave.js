/**
 * Course Builder Auto-save Module
 * Handles auto-save functionality for forms
 */

function initializeAutoSave() {
    // Auto-save disabled - using manual save instead
    // initializeCourseAutoSave();
}

function initializeCourseAutoSave() {
    // Auto-save for course details
    const courseForm = document.getElementById('course-details-form');
    if (courseForm) {
        const inputs = courseForm.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // Remove existing listeners to prevent duplicates
            input.removeEventListener('input', input._autoSaveHandler);
            input.removeEventListener('change', input._autoSaveHandler);

            const eventType = input.tagName === 'SELECT' ? 'change' : 'input';
            const handler = function() {
                scheduleAutoSave('course', courseId, () => autoSaveCourse());
            };

            // Store handler reference for removal
            input._autoSaveHandler = handler;
            input.addEventListener(eventType, handler);
        });
    }
}

function scheduleAutoSave(type, id, saveFunction) {
    const key = `${type}-${id}`;

    // Clear existing timeout
    if (autoSaveTimeouts.has(key)) {
        clearTimeout(autoSaveTimeouts.get(key));
    }

    // Show saving indicator
    showSaveStatus('saving', 'Saving...');

    // Schedule new save
    const timeoutId = setTimeout(() => {
        saveFunction();
        autoSaveTimeouts.delete(key);
    }, 1000); // 1 second delay

    autoSaveTimeouts.set(key, timeoutId);
}

function showSaveStatus(status, message) {
    const indicator = document.getElementById('save-status-indicator');
    const statusText = document.getElementById('save-status-text');

    if (!indicator || !statusText) return;

    // Remove existing status classes
    indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

    switch (status) {
        case 'saving':
            indicator.className += ' bg-blue-600 text-white';
            statusText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
            break;
        case 'saved':
            indicator.className += ' bg-green-600 text-white';
            statusText.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
            break;
        case 'error':
            indicator.className += ' bg-red-600 text-white';
            statusText.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
            break;
    }

    indicator.classList.remove('hidden');

    // Auto-hide success/error messages
    if (status !== 'saving') {
        setTimeout(() => {
            indicator.classList.add('hidden');
        }, 3000);
    }
}

function showFileUploadIndicator(lectureId, show) {
    const indicator = document.getElementById(`file-upload-indicator-${lectureId}`);
    if (!indicator) return;

    if (show) {
        indicator.classList.remove('hidden');
    } else {
        indicator.classList.add('hidden');
    }
}

function initializeChapterAutoSave(chapterId) {
    const chapterForm = document.getElementById(`chapter-form-${chapterId}`);
    if (chapterForm) {
        const inputs = chapterForm.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // Remove existing listeners to prevent duplicates
            input.removeEventListener('input', input._autoSaveHandler);
            input.removeEventListener('change', input._autoSaveHandler);

            const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
            const handler = function() {
                scheduleAutoSave('chapter', chapterId, () => autoSaveChapter(chapterId));
            };

            // Store handler reference for removal
            input._autoSaveHandler = handler;
            input.addEventListener(eventType, handler);
        });
    }
}

function initializeLectureAutoSave(lectureId) {
    const lectureForm = document.getElementById(`lecture-form-${lectureId}`);
    if (!lectureForm) {
        console.error('Lecture form not found for auto-save initialization:', lectureId);
        return;
    }

    // Get all form inputs, including those in hidden content sections
    const inputs = lectureForm.querySelectorAll('input, textarea, select');

    inputs.forEach(input => {
        // Remove existing listeners to prevent duplicates
        if (input._lectureAutoSaveHandler) {
            input.removeEventListener('input', input._lectureAutoSaveHandler);
            input.removeEventListener('change', input._lectureAutoSaveHandler);
        }

        // Special handling for file inputs
        if (input.type === 'file' && input.name === 'resource_file') {
            const fileHandler = function() {
                if (input.files && input.files.length > 0) {
                    console.log('File selected for lecture:', lectureId, input.files[0].name);
                    // Auto-save when file is selected
                    setTimeout(() => {
                        autoSaveLecture(lectureId);
                    }, 500);
                }
            };
            input._lectureAutoSaveHandler = fileHandler;
            input.addEventListener('change', fileHandler);
        } else {
            // Regular form inputs
            const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
            const handler = function() {
                // Add a small delay to ensure the form state is updated
                setTimeout(() => {
                    scheduleAutoSave('lecture', lectureId, () => autoSaveLecture(lectureId));
                }, 100);
            };

            input._lectureAutoSaveHandler = handler;
            input.addEventListener(eventType, handler);
        }
    });
}
