/**
 * Course Builder Lecture Editor Module
 * Handles rendering and functionality of the lecture editor
 */

function renderLectureEditor(lectureId, lecture) {
    const lectureEditor = document.getElementById('lecture-editor');
    if (!lectureEditor) return;

    // Build the lecture editor HTML
    const editorHTML = `
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white">Edit Lecture</h3>
                <button onclick="deleteLecture('${lectureId}')"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete Lecture
                </button>
            </div>

            <form id="lecture-form-${lectureId}" class="space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Lecture Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" value="${lecture.title || ''}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                               placeholder="Enter lecture title">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Lecture Type <span class="text-red-500">*</span>
                        </label>
                        <select name="type" onchange="toggleLectureContent('${lectureId}', this.value)"
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            <option value="video" ${lecture.type === 'video' ? 'selected' : ''}>Video</option>
                            <option value="text" ${lecture.type === 'text' ? 'selected' : ''}>Text/Article</option>
                            <option value="quiz" ${lecture.type === 'quiz' ? 'selected' : ''}>Quiz</option>
                            <option value="assignment" ${lecture.type === 'assignment' ? 'selected' : ''}>Assignment</option>
                            <option value="resource" ${lecture.type === 'resource' ? 'selected' : ''}>Resource/Download</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Lecture Description
                    </label>
                    <textarea name="description" rows="3"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Describe what this lecture covers">${lecture.description || ''}</textarea>
                </div>

                <!-- Duration and Settings -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Duration (minutes)
                        </label>
                        <input type="number" name="duration_minutes" value="${lecture.duration_minutes || ''}" min="0" max="999"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                               placeholder="0">
                    </div>

                    <div>
                        <label class="flex items-center space-x-3 mt-8">
                            <input type="checkbox" name="is_published" ${lecture.is_published ? 'checked' : ''}
                                   class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                            <span class="text-gray-300">Published</span>
                        </label>
                    </div>

                    <div>
                        <label class="flex items-center space-x-3 mt-8">
                            <input type="checkbox" name="is_free_preview" ${lecture.is_free_preview ? 'checked' : ''}
                                   class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                            <span class="text-gray-300">Free Preview</span>
                        </label>
                    </div>
                </div>

                <!-- Content Sections -->
                ${generateLectureContentSections(lectureId, lecture)}

                <!-- Save Button -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                    <button type="button" onclick="saveLecture('${lectureId}')"
                            class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>Save Lecture
                    </button>
                </div>
            </form>
        </div>
    `;

    lectureEditor.innerHTML = editorHTML;

    // Initialize file upload functionality for resource lectures
    if (lecture.type === 'resource') {
        initializeFileUpload(lectureId);
    }

    // Initialize auto-save for the lecture form
    // initializeLectureAutoSave(lectureId);
}

function generateLectureContentSections(lectureId, lecture) {
    const type = lecture.type || 'video';
    
    let contentHTML = '';

    // Video Content Section
    contentHTML += `
        <div id="video-content-${lectureId}" class="lecture-content ${type !== 'video' ? 'hidden' : ''}">
            <h4 class="text-lg font-semibold text-white mb-4">Video Content</h4>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Video URL <span class="text-red-500">*</span>
                    </label>
                    <input type="url" name="video_url" value="${lecture.video_url || ''}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                           placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                    <p class="text-xs text-gray-400 mt-1">Supports YouTube, Vimeo, and direct video URLs</p>
                </div>
            </div>
        </div>
    `;

    // Text Content Section
    contentHTML += `
        <div id="text-content-${lectureId}" class="lecture-content ${type !== 'text' ? 'hidden' : ''}">
            <h4 class="text-lg font-semibold text-white mb-4">Text Content</h4>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Article Content <span class="text-red-500">*</span>
                    </label>
                    <textarea name="text_content" rows="10"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Write your article content here...">${lecture.text_content || ''}</textarea>
                    <p class="text-xs text-gray-400 mt-1">Supports Markdown formatting</p>
                </div>
            </div>
        </div>
    `;

    // Quiz Content Section
    contentHTML += `
        <div id="quiz-content-${lectureId}" class="lecture-content ${type !== 'quiz' ? 'hidden' : ''}">
            <h4 class="text-lg font-semibold text-white mb-4">Quiz Content</h4>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Quiz Instructions
                    </label>
                    <textarea name="quiz_instructions" rows="3"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Instructions for students taking this quiz...">${lecture.quiz_instructions || ''}</textarea>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Passing Score (%)
                        </label>
                        <input type="number" name="quiz_passing_score" value="${lecture.quiz_passing_score || 70}" min="0" max="100"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Time Limit (minutes)
                        </label>
                        <input type="number" name="quiz_time_limit" value="${lecture.quiz_time_limit || ''}" min="1" max="180"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                               placeholder="Optional">
                    </div>
                </div>
            </div>
        </div>
    `;

    // Assignment Content Section
    contentHTML += `
        <div id="assignment-content-${lectureId}" class="lecture-content ${type !== 'assignment' ? 'hidden' : ''}">
            <h4 class="text-lg font-semibold text-white mb-4">Assignment Content</h4>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Assignment Instructions <span class="text-red-500">*</span>
                    </label>
                    <textarea name="assignment_instructions" rows="6"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Provide detailed instructions for the assignment...">${lecture.assignment_instructions || ''}</textarea>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Max Points
                        </label>
                        <input type="number" name="assignment_max_points" value="${lecture.assignment_max_points || 100}" min="1" max="1000"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Due Date
                        </label>
                        <input type="datetime-local" name="assignment_due_date" value="${lecture.assignment_due_date || ''}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500">
                    </div>
                </div>
            </div>
        </div>
    `;

    // Resource Content Section
    contentHTML += `
        <div id="resource-content-${lectureId}" class="lecture-content ${type !== 'resource' ? 'hidden' : ''}">
            <h4 class="text-lg font-semibold text-white mb-4">Resource Content</h4>
            <div class="space-y-6">
                <!-- Enhanced File Upload Area -->
                <div class="resource-upload-container" data-lecture-id="${lectureId}">
                    <label class="block text-sm font-medium text-gray-300 mb-3">
                        Resource Files <span class="text-red-500">*</span>
                    </label>

                    <!-- File Upload Zone -->
                    <div class="file-upload-zone" id="file-upload-zone-${lectureId}">
                        <div class="upload-area border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-red-500 transition-colors cursor-pointer bg-gray-800/50 hover:bg-gray-800/70">
                            <div class="upload-icon mb-4">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400"></i>
                            </div>
                            <div class="upload-text">
                                <p class="text-lg font-medium text-white mb-2">Drop files here or click to browse</p>
                                <p class="text-sm text-gray-400">Supports PDF, DOC, PPT, XLS, ZIP, images and more (Max: 50MB per file)</p>
                            </div>
                            <input type="file"
                                   id="resource-files-${lectureId}"
                                   name="resource_files[]"
                                   multiple
                                   accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.jpg,.jpeg,.png,.gif,.txt,.csv"
                                   class="hidden">
                        </div>
                    </div>

                    <!-- File List Container -->
                    <div class="uploaded-files-container mt-4" id="uploaded-files-${lectureId}">
                        <!-- Existing files will be loaded here -->
                        ${generateExistingFilesList(lecture.resources || [], lectureId)}
                    </div>

                    <!-- Upload Progress Container -->
                    <div class="upload-progress-container mt-4 hidden" id="upload-progress-${lectureId}">
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-white">Uploading files...</span>
                                <span class="text-sm text-gray-400" id="upload-status-${lectureId}">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-red-600 h-2 rounded-full transition-all duration-300"
                                     id="upload-progress-bar-${lectureId}"
                                     style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resource Description -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Resource Description
                    </label>
                    <textarea name="resource_description" rows="3"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                              placeholder="Describe what these resources contain...">${lecture.resource_description || ''}</textarea>
                </div>
            </div>
        </div>
    `;

    return contentHTML;
}

function toggleLectureContent(lectureId, type) {
    console.log(`Toggling lecture content for lecture ${lectureId} to type: ${type}`);

    // Hide all content sections
    const contentSections = document.querySelectorAll(`#video-content-${lectureId}, #text-content-${lectureId}, #quiz-content-${lectureId}, #assignment-content-${lectureId}, #resource-content-${lectureId}`);
    contentSections.forEach(section => {
        if (section) {
            section.classList.add('hidden');
        }
    });

    // Show the selected content section
    const targetSection = document.getElementById(`${type}-content-${lectureId}`);
    if (targetSection) {
        targetSection.classList.remove('hidden');
    }

    // Initialize file upload functionality if switching to resource type
    if (type === 'resource') {
        setTimeout(() => {
            initializeFileUpload(lectureId);
        }, 100);
    }

    // Re-initialize auto-save for the lecture form to include new visible inputs
    setTimeout(() => {
        // initializeLectureAutoSave(lectureId);
    }, 100);
}

/**
 * Generate HTML for existing files list
 */
function generateExistingFilesList(resources, lectureId) {
    if (!resources || !Array.isArray(resources) || resources.length === 0) {
        return '<div class="no-files-message text-center py-4 text-gray-400 text-sm">No files uploaded yet</div>';
    }

    let filesHTML = '<div class="files-grid space-y-3">';

    resources.forEach((file, index) => {
        const fileSize = formatFileSize(file.file_size || file.size || 0);
        const fileIcon = getFileIcon(file.file_type || file.type || '');
        const fileName = file.name || file.original_name || 'Unknown file';

        filesHTML += `
            <div class="file-item bg-gray-800 border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-colors" data-file-index="${index}">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3 flex-1 min-w-0">
                        <div class="file-icon text-2xl">
                            ${fileIcon}
                        </div>
                        <div class="file-info flex-1 min-w-0">
                            <p class="text-white font-medium truncate" title="${fileName}">${fileName}</p>
                            <p class="text-gray-400 text-sm">${fileSize}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button type="button"
                                class="preview-btn text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-gray-700 transition-colors"
                                onclick="previewFile('${file.file_path || file.path}', '${fileName}')"
                                title="Preview file">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button"
                                class="download-btn text-green-400 hover:text-green-300 p-2 rounded-lg hover:bg-gray-700 transition-colors"
                                onclick="downloadFile('${file.file_path || file.path}', '${fileName}')"
                                title="Download file">
                            <i class="fas fa-download"></i>
                        </button>
                        <button type="button"
                                class="delete-btn text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-gray-700 transition-colors"
                                onclick="deleteFile(${index}, '${lectureId}')"
                                title="Delete file">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    filesHTML += '</div>';
    return filesHTML;
}
