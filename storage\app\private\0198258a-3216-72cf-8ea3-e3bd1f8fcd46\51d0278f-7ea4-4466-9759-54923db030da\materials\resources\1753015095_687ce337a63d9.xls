<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Data Export Indah Berkah Abadi</Title>
  <Author>Indah Berkah Abadi</Author>
  <Created>2025-06-20T02:26:32.094881Z</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>15000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="16" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#1E40AF" ss:Pattern="Solid"/>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="s63">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="12" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#3B82F6" ss:Pattern="Solid"/>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="s64">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <Interior ss:Color="#F3F4F6" ss:Pattern="Solid"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s65">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s66">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Right"/>
  </Style>
  <Style ss:ID="s67">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center"/>
  </Style>
 </Styles><Worksheet ss:Name="Data Produk">
  <Table><Row>
   <Cell ss:MergeAcross="5" ss:StyleID="s62"><Data ss:Type="String">DATA PRODUK - INDAH BERKAH ABADI</Data></Cell>
  </Row><Row>
   <Cell ss:MergeAcross="5" ss:StyleID="s62"><Data ss:Type="String">Diunduh pada: 20/06/2025 10:26:32 Asia/Makassar</Data></Cell>
  </Row>
  <Row></Row><Row><Cell ss:StyleID="s64"><Data ss:Type="String">No</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Nama Produk</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Stok Gudang</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Total Distribusi</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Stok Toko</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Dibuat Pada</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Smartphone Android Premium</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">58</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">2</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Laptop Gaming High-End</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">145</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">3</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Power Bank 20000mAh</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">102</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">24</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">4</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Mouse Gaming RGB</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">54</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">5</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Keyboard Mechanical</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">185</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">28</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">6</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Monitor LED 24 inch</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">148</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">18</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">7</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Speaker Bluetooth Portable</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">85</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row></Table>
 </Worksheet><Worksheet ss:Name="Data Toko">
  <Table><Row>
   <Cell ss:MergeAcross="5" ss:StyleID="s62"><Data ss:Type="String">DATA TOKO - INDAH BERKAH ABADI</Data></Cell>
  </Row><Row>
   <Cell ss:MergeAcross="5" ss:StyleID="s62"><Data ss:Type="String">Diunduh pada: 20/06/2025 10:26:32 Asia/Makassar</Data></Cell>
  </Row>
  <Row></Row><Row><Cell ss:StyleID="s64"><Data ss:Type="String">No</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Nama Toko</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Lokasi</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Jumlah Pengguna</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Total Distribusi</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Dibuat Pada</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Gudang Pusat</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Balikpapan</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:49</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">2</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Toko Jakarta</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Jakarta Pusat</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">3</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:49</Data></Cell></Row></Table>
 </Worksheet><Worksheet ss:Name="Data Distribusi">
  <Table><Row>
   <Cell ss:MergeAcross="8" ss:StyleID="s62"><Data ss:Type="String">DATA DISTRIBUSI - INDAH BERKAH ABADI</Data></Cell>
  </Row><Row>
   <Cell ss:MergeAcross="8" ss:StyleID="s62"><Data ss:Type="String">Periode: 20/05/2025 - 20/06/2025</Data></Cell>
  </Row><Row>
   <Cell ss:MergeAcross="8" ss:StyleID="s62"><Data ss:Type="String">Diunduh pada: 20/06/2025 10:26:32 Asia/Makassar</Data></Cell>
  </Row>
  <Row></Row><Row><Cell ss:StyleID="s64"><Data ss:Type="String">No</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Tanggal Distribusi</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Produk</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Toko</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Jumlah</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Jumlah Diterima</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Status</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Catatan</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Dibuat Pada</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">20/06/2025</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Keyboard Mechanical</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Toko Jakarta</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">12</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">0</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Belum Dikonfirmasi</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String"></Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">20/06/2025 10:24:59</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">2</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">18/06/2025</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Smartphone Android Premium</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Toko Jakarta</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">12</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">12</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Dikonfirmasi</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Ada sedikit kerusakan pada kemasan</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">3</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">18/06/2025</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Monitor LED 24 inch</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Toko Jakarta</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">38</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">38</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Dikonfirmasi</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String"></Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row></Table>
 </Worksheet></Workbook>